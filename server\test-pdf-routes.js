const express = require('express');
const app = express();

// Test if the PDF extraction route is properly loaded
try {
  const pdfRoute = require('./routes/pdfQuestionExtractionRoute');
  console.log('✅ PDF extraction route loaded successfully');
  
  // Test if the service is properly loaded
  const PDFService = require('./services/pdfQuestionExtractionService');
  console.log('✅ PDF extraction service loaded successfully');
  
  // Test if required dependencies are available
  const pdf = require('pdf-parse');
  console.log('✅ pdf-parse dependency available');
  
  const OpenAI = require('openai');
  console.log('✅ openai dependency available');
  
  console.log('\n🎉 All PDF extraction components are properly loaded!');
  console.log('\n📋 To test the feature:');
  console.log('1. Login as admin at http://localhost:3000/login');
  console.log('2. Go to Admin → Exams → Edit any exam');
  console.log('3. Click "Questions" tab');
  console.log('4. Look for "Extract from PDF" button');
  
} catch (error) {
  console.error('❌ Error loading PDF extraction components:', error.message);
}
