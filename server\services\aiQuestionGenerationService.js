const axios = require("axios");
const { AIQuestionGeneration, QuestionTemplate } = require("../models/aiQuestionGenerationModel");
const Question = require("../models/questionModel");
const WebImageService = require("./webImageService");
const QuestionValidationService = require("./questionValidationService");
const SyllabusService = require("./syllabusService");
const {
  primarySyllabus,
  secondarySyllabus,
  advanceSyllabus,
  questionGenerationGuidelines,
  subjectQuestionTypes
} = require("../data/tanzaniaSyllabus");

class AIQuestionGenerationService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.model = process.env.OPENAI_MODEL || "gpt-3.5-turbo";
    this.webImageService = new WebImageService();
    this.validationService = new QuestionValidationService();

    // Track generated topics to prevent repetition within a session
    this.generatedTopics = new Map(); // key: examId, value: Set of topics

    // Debug logging for API key and model
    if (!this.openaiApiKey) {
      console.error("❌ OpenAI API key not found in environment variables");
    } else {
      console.log("✅ OpenAI API key loaded successfully");
      console.log(`✅ Using OpenAI model: ${this.model}`);
    }
  }

  // Track topics to prevent repetition
  trackGeneratedTopic(examId, topic) {
    if (!this.generatedTopics.has(examId)) {
      this.generatedTopics.set(examId, new Set());
    }
    this.generatedTopics.get(examId).add(topic);
  }

  // Get previously generated topics for an exam
  getGeneratedTopics(examId) {
    return this.generatedTopics.get(examId) || new Set();
  }

  // Clear topic tracking for an exam (when starting fresh)
  clearTopicTracking(examId) {
    this.generatedTopics.delete(examId);
  }

  // Main method to generate questions
  async generateQuestions(generationParams, requestedBy, examId) {
    const startTime = Date.now();
    console.log(`🚀 Starting AI question generation for ${generationParams.totalQuestions} questions...`);

    // If no examId provided, create a new exam automatically
    let finalExamId = examId;
    if (!examId) {
      console.log('📝 No examId provided, creating new exam automatically...');
      finalExamId = await this.createAutoExam(generationParams, requestedBy);
      console.log(`✅ Auto-created exam with ID: ${finalExamId}`);
    }

    // Clear previous topic tracking for this exam to start fresh
    this.clearTopicTracking(finalExamId);

    try {
      // Create generation record
      const generationRecord = new AIQuestionGeneration({
        requestedBy,
        examId: finalExamId,
        generationParams,
        generationStatus: "in_progress",
        aiModel: this.model,
      });
      await generationRecord.save();

      const generatedQuestions = [];
      const { questionDistribution } = generationParams;

      // Generate multiple choice questions
      if (questionDistribution.multiple_choice > 0) {
        try {
          console.log(`📝 Generating ${questionDistribution.multiple_choice} multiple choice questions...`);
          const mcQuestions = await this.generateMultipleChoiceQuestions(
            generationParams,
            questionDistribution.multiple_choice
          );
          generatedQuestions.push(...mcQuestions);
          console.log(`✅ Generated ${mcQuestions.length}/${questionDistribution.multiple_choice} multiple choice questions`);
        } catch (error) {
          console.error("❌ Failed to generate multiple choice questions:", error.message);
        }
      }

      // Generate fill in the blank questions
      if (questionDistribution.fill_blank > 0) {
        try {
          console.log(`📝 Generating ${questionDistribution.fill_blank} fill-in-the-blank questions...`);
          const fillBlankQuestions = await this.generateFillBlankQuestions(
            generationParams,
            questionDistribution.fill_blank
          );
          generatedQuestions.push(...fillBlankQuestions);
          console.log(`✅ Generated ${fillBlankQuestions.length}/${questionDistribution.fill_blank} fill in the blank questions`);
        } catch (error) {
          console.error("❌ Failed to generate fill in the blank questions:", error.message);
        }
      }

      // Generate picture-based questions
      if (questionDistribution.picture_based > 0) {
        try {
          console.log(`📝 Generating ${questionDistribution.picture_based} picture-based questions...`);
          const pictureQuestions = await this.generatePictureBasedQuestions(
            generationParams,
            questionDistribution.picture_based
          );
          generatedQuestions.push(...pictureQuestions);
          console.log(`✅ Generated ${pictureQuestions.length}/${questionDistribution.picture_based} picture-based questions`);
        } catch (error) {
          console.error("❌ Failed to generate picture-based questions:", error.message);
          console.log("ℹ️ Picture-based questions require image services. Continuing with other question types...");
        }
      }

      // Check if any questions were generated
      if (generatedQuestions.length === 0) {
        throw new Error("No questions could be generated. Please check your internet connection and try again.");
      }

      console.log(`📊 Total questions generated: ${generatedQuestions.length}`);

      // Validate generated questions
      const validatedQuestions = generatedQuestions.map(question => {
        const validation = this.validationService.validateQuestion(
          question,
          generationParams.level,
          generationParams.class,
          generationParams.subjects[0] // Use first subject for validation
        );

        return {
          ...question,
          validation,
        };
      });

      // Update generation record
      const endTime = Date.now();
      generationRecord.generatedQuestions = validatedQuestions.map(q => ({
        generatedContent: q, // Now we can store the entire object since we're using Mixed type
        approved: false,
        qualityScore: q.validation?.score || 0,
      }));
      generationRecord.generationStatus = "completed";
      generationRecord.generationTime = endTime - startTime;

      // Calculate overall quality score
      const avgQualityScore = validatedQuestions.reduce((sum, q) => sum + (q.validation?.score || 0), 0) / validatedQuestions.length;
      generationRecord.qualityScore = Math.round(avgQualityScore);

      await generationRecord.save();

      return {
        success: true,
        generationId: generationRecord._id,
        examId: finalExamId, // Include the exam ID in response
        questions: generatedQuestions,
        generationTime: endTime - startTime,
      };

    } catch (error) {
      console.error("Question generation error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Create an automatic exam for standalone question generation
  async createAutoExam(generationParams, requestedBy) {
    const Exam = require("../models/examModel");

    // Generate a unique exam name
    const randomId = Math.random().toString(36).substring(2, 4).toUpperCase();
    const subjectAbbr = generationParams.subjects.length === 1
      ? generationParams.subjects[0].substring(0, 2).toUpperCase()
      : generationParams.subjects.length <= 2
        ? generationParams.subjects.map(s => s.charAt(0)).join("").toUpperCase()
        : "MX"; // Multi-subject

    const levelAbbr = generationParams.level.charAt(0).toUpperCase(); // P, S, A
    const examName = `${levelAbbr}${generationParams.class}${subjectAbbr}-${randomId}`;

    // Create descriptive name for display
    const subjectList = generationParams.subjects.length <= 2
      ? generationParams.subjects.join(" & ")
      : generationParams.subjects.length === 3
        ? generationParams.subjects.slice(0, 2).join(", ") + " & " + generationParams.subjects[2]
        : generationParams.subjects.slice(0, 2).join(", ") + ` & ${generationParams.subjects.length - 2} more`;

    // Calculate total marks (assuming 1 mark per question)
    const totalMarks = generationParams.totalQuestions;
    const passingMarks = Math.ceil(totalMarks * 0.6); // 60% pass mark

    // Create the exam
    const examData = {
      name: examName,
      duration: Math.max(30, generationParams.totalQuestions * 2), // 2 minutes per question, minimum 30 minutes
      category: generationParams.subjects[0], // Use first subject as category
      subject: generationParams.subjects[0],
      level: generationParams.level,
      class: generationParams.class,
      totalMarks: totalMarks,
      passingMarks: passingMarks,
      passingPercentage: 60,
      difficulty: generationParams.difficultyLevels?.[0] || "medium",
      difficultyLevel: generationParams.difficultyLevels?.[0] || "medium",
      questions: [], // Start with empty questions array
    };

    console.log(`📝 Creating auto exam: ${examName}`);

    const newExam = new Exam(examData);
    await newExam.save();

    // Send notification about new exam
    try {
      const NotificationService = require("../services/notificationService");
      console.log('🔔 Sending new auto-generated exam notification for:', newExam.name);
      await NotificationService.notifyNewExam(newExam);
      console.log('✅ New auto-generated exam notification sent successfully');
    } catch (notifError) {
      console.error('❌ Error sending new auto-generated exam notification:', notifError);
      // Don't fail the exam creation if notification fails
    }

    return newExam._id;
  }

  // Generate multiple choice questions
  async generateMultipleChoiceQuestions(params, count) {
    const { level, class: className, subjects, difficultyLevels, syllabusTopics, selectedSyllabusId } = params;
    const questions = [];
    const usedQuestions = new Set(); // Track question uniqueness
    const usedTopics = new Map(); // Track topic usage for diversity
    let attempts = 0;
    const maxAttempts = count * 3; // Allow more attempts to find unique questions

    while (questions.length < count && attempts < maxAttempts) {
      attempts++;
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const difficulty = difficultyLevels[Math.floor(Math.random() * difficultyLevels.length)];

      // Get available topics for diversity
      const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);
      const availableTopics = this.extractAvailableTopics(syllabusData);

      // Select a topic that hasn't been overused
      const selectedTopic = this.selectDiverseTopic(availableTopics, usedTopics, count);

      const prompt = await this.buildMultipleChoicePrompt(level, className, subject, difficulty, [selectedTopic], selectedSyllabusId);

      // Add uniqueness and diversity instructions to prompt
      const enhancedPrompt = prompt + `\n\nIMPORTANT INSTRUCTIONS:
1. Generate a UNIQUE question. Avoid these patterns: ${Array.from(usedQuestions).slice(-5).join(', ')}
2. Focus specifically on the topic: ${selectedTopic}
3. Ensure this is STRICTLY ${subject} content - NO other subjects
4. Make sure the question is appropriate for Class ${className} level only`;

      try {
        const response = await this.callOpenAI(enhancedPrompt);
        const questionData = this.parseJSONResponse(response);

        if (questionData && questionData.question) {
          // Validate subject alignment
          if (!this.validateSubjectAlignment(questionData.question, subject)) {
            console.log(`⚠️ Question rejected for subject mixing: ${questionData.question.substring(0, 50)}...`);
            continue;
          }

          // Check for uniqueness
          const questionKey = this.generateQuestionKey(questionData.question);

          if (!usedQuestions.has(questionKey)) {
            usedQuestions.add(questionKey);

            // Track topic usage
            const questionTopic = questionData.syllabusTopics?.[0] || selectedTopic;
            usedTopics.set(questionTopic, (usedTopics.get(questionTopic) || 0) + 1);

            questions.push({
              name: questionData.question,
              type: "mcq", // New unified type field
              correctAnswer: questionData.correctOption, // Unified correct answer field
              options: questionData.options,
              topic: questionTopic,
              classLevel: `${level} ${className}`,
              // Legacy fields for backward compatibility
              answerType: "Options",
              correctOption: questionData.correctOption,
              isAIGenerated: true,
              generationSource: "ai_bulk",
              difficultyLevel: difficulty,
              syllabusTopics: questionData.syllabusTopics || [selectedTopic],
              questionType: "multiple_choice",
              createdBy: "ai",
              competencyAligned: questionData.competencyAligned || "General",
            });
            console.log(`✅ Generated unique MCQ ${questions.length}/${count}`);
          } else {
            console.log(`⚠️ Duplicate question detected, retrying...`);
          }
        }
      } catch (error) {
        console.error(`❌ Error generating MC question attempt ${attempts}:`, error.message);
        // Continue with next attempt
      }
    }

    return questions;
  }

  // Generate a key for question uniqueness checking
  generateQuestionKey(questionText) {
    return questionText
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize spaces
      .trim()
      .substring(0, 50); // Use first 50 characters as key
  }

  // Extract available topics from syllabus data
  extractAvailableTopics(syllabusData) {
    const topics = [];

    if (syllabusData.topics && typeof syllabusData.topics === 'object') {
      Object.keys(syllabusData.topics).forEach(topicKey => {
        topics.push(topicKey);
        if (syllabusData.topics[topicKey].subtopics) {
          topics.push(...syllabusData.topics[topicKey].subtopics);
        }
      });
    }

    // Add topics from learning objectives
    if (syllabusData.learningObjectives) {
      syllabusData.learningObjectives.forEach(obj => {
        if (obj.topic && !topics.includes(obj.topic)) {
          topics.push(obj.topic);
        }
      });
    }

    return topics.length > 0 ? topics : ['General'];
  }

  // Select a diverse topic to avoid repetition
  selectDiverseTopic(availableTopics, usedTopics, totalCount) {
    if (availableTopics.length === 0) return 'General';

    // Calculate max usage per topic for even distribution
    const maxUsagePerTopic = Math.ceil(totalCount / availableTopics.length);

    // Find topics that haven't reached max usage
    const underusedTopics = availableTopics.filter(topic =>
      (usedTopics.get(topic) || 0) < maxUsagePerTopic
    );

    // If all topics are at max usage, use any topic
    const candidateTopics = underusedTopics.length > 0 ? underusedTopics : availableTopics;

    return candidateTopics[Math.floor(Math.random() * candidateTopics.length)];
  }

  // Validate that question content aligns with the specified subject
  validateSubjectAlignment(questionText, subject) {
    const questionLower = questionText.toLowerCase();

    // Define subject-specific keywords
    const subjectKeywords = {
      'Science and Technology': ['science', 'technology', 'experiment', 'laboratory', 'chemical', 'physics', 'biology', 'plant', 'animal', 'matter', 'energy', 'force', 'motion', 'tool', 'machine', 'computer', 'renewable', 'environment', 'nature', 'living', 'non-living', 'material', 'water', 'air', 'soil', 'light', 'sound', 'heat'],
      'Mathematics': ['number', 'calculate', 'add', 'subtract', 'multiply', 'divide', 'fraction', 'decimal', 'geometry', 'shape', 'area', 'volume', 'equation', 'solve', 'plus', 'minus', 'times', 'divided'],
      'English': ['grammar', 'sentence', 'verb', 'noun', 'adjective', 'paragraph', 'story', 'poem', 'reading', 'writing', 'letter', 'word', 'spelling', 'pronunciation'],
      'Kiswahili': ['lugha', 'sentensi', 'maneno', 'mazungumzo', 'insha', 'mashairi', 'kiswahili', 'lugha ya kiswahili'],
      'Geography': ['map', 'continent', 'country', 'climate', 'weather', 'mountain', 'river', 'ocean', 'location', 'direction', 'compass'],
      'History': ['past', 'ancient', 'colonial', 'independence', 'historical', 'century', 'era', 'war', 'leader', 'empire'],
      'Civics': ['government', 'citizen', 'rights', 'democracy', 'constitution', 'law', 'society', 'election', 'vote']
    };

    // For Science and Technology, be more lenient as it covers broad topics
    if (subject === 'Science and Technology') {
      // Check for obvious math-only content
      const mathOnlyKeywords = ['calculate', 'add', 'subtract', 'multiply', 'divide', 'equation', 'solve', 'plus', 'minus', 'times', 'divided'];
      const hasMathOnly = mathOnlyKeywords.some(keyword => questionLower.includes(keyword));

      // Check for obvious language-only content
      const languageOnlyKeywords = ['grammar', 'sentence structure', 'verb tense', 'adjective', 'paragraph writing', 'spelling'];
      const hasLanguageOnly = languageOnlyKeywords.some(keyword => questionLower.includes(keyword));

      // Reject only if it's clearly math or language focused
      return !hasMathOnly && !hasLanguageOnly;
    }

    // For other subjects, use the original logic
    const currentSubjectKeywords = subjectKeywords[subject] || [];
    const hasCurrentSubjectKeywords = currentSubjectKeywords.some(keyword =>
      questionLower.includes(keyword)
    );

    // Check for contamination from other subjects (excluding Science and Technology)
    const otherSubjects = Object.keys(subjectKeywords).filter(s => s !== subject && s !== 'Science and Technology');
    const hasOtherSubjectKeywords = otherSubjects.some(otherSubject =>
      subjectKeywords[otherSubject].some(keyword => questionLower.includes(keyword))
    );

    // Question is valid if it has current subject keywords and no other subject keywords
    return hasCurrentSubjectKeywords && !hasOtherSubjectKeywords;
  }

  // Generate fill in the blank questions
  async generateFillBlankQuestions(params, count) {
    const { level, class: className, subjects, difficultyLevels, syllabusTopics, selectedSyllabusId } = params;
    const questions = [];

    for (let i = 0; i < count; i++) {
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const difficulty = difficultyLevels[Math.floor(Math.random() * difficultyLevels.length)];

      const prompt = await this.buildFillBlankPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId);
      
      try {
        const response = await this.callOpenAI(prompt);
        const questionData = this.parseJSONResponse(response);

        questions.push({
          name: questionData.question,
          type: "fill", // New unified type field
          correctAnswer: questionData.correctAnswer,
          topic: questionData.topic || subject,
          classLevel: `${level} ${className}`,
          // Legacy fields for backward compatibility
          answerType: "Fill in the Blank",
          isAIGenerated: true,
          generationSource: "ai_bulk",
          difficultyLevel: difficulty,
          syllabusTopics: questionData.syllabusTopics || [],
          questionType: "fill_blank",
          createdBy: "ai",
        });
      } catch (error) {
        console.error(`❌ Error generating fill blank question ${i + 1}:`, error.message);
        // Continue with next question
      }
    }

    return questions;
  }

  // Generate picture-based questions
  async generatePictureBasedQuestions(params, count) {
    const { level, class: className, subjects, difficultyLevels, syllabusTopics, selectedSyllabusId } = params;
    const questions = [];

    for (let i = 0; i < count; i++) {
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const difficulty = difficultyLevels[Math.floor(Math.random() * difficultyLevels.length)];

      const prompt = await this.buildPictureBasedPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId);
      
      try {
        const response = await this.callOpenAI(prompt);
        const questionData = this.parseJSONResponse(response);

        // Search for appropriate images
        let imageUrl = null;
        try {
          const images = await this.webImageService.searchImagesForQuestion(
            subject,
            questionData.syllabusTopics?.[0] || "general",
            "picture_based",
            difficulty
          );

          if (images.length > 0) {
            // Use the most relevant image
            const selectedImage = images[0];

            // Download and upload to S3 for permanent storage
            const uploadResult = await this.webImageService.downloadAndUploadImage(
              selectedImage.url,
              `temp-${Date.now()}`
            );

            if (uploadResult.success) {
              imageUrl = uploadResult.imageUrl;
            }
          }
        } catch (imageError) {
          console.error("⚠️ Image service unavailable:", imageError.message);
          // Continue without image - the question can still be used as text-based
          console.log("ℹ️ Generating picture-based question without image (can be added manually later)");
        }

        questions.push({
          name: questionData.question,
          type: "image", // New unified type field
          correctAnswer: questionData.correctOption, // Unified correct answer field
          options: questionData.options,
          imageUrl: imageUrl, // New imageUrl field
          topic: questionData.topic || subject,
          classLevel: `${level} ${className}`,
          // Legacy fields for backward compatibility
          answerType: "Options",
          correctOption: questionData.correctOption,
          image: imageUrl,
          isAIGenerated: true,
          generationSource: "ai_bulk",
          difficultyLevel: difficulty,
          syllabusTopics: questionData.syllabusTopics || [],
          questionType: "picture_based",
          imageDescription: questionData.imageDescription,
          createdBy: "ai",
        });
      } catch (error) {
        console.error(`❌ Error generating picture question ${i + 1}:`, error.message);
        // Continue with next question
      }
    }

    return questions;
  }

  // Build prompts for different question types
  async buildMultipleChoicePrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId = null) {
    const guidelines = questionGenerationGuidelines[level];
    const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);

    // Build enhanced prompt with structured syllabus content
    let syllabusContent = "";
    let competenciesContent = "";
    let learningObjectivesContent = "";
    let classSpecificContent = "";

    if ((syllabusData.source === 'pdf' || syllabusData.source === 'selected_pdf')) {
      // Add competencies if available
      if (syllabusData.competencies && syllabusData.competencies.length > 0) {
        competenciesContent = `\n\nMAIN COMPETENCIES:\n${syllabusData.competencies.map(comp =>
          `- ${comp.name}: ${comp.description}`
        ).join('\n')}`;
      }

      // Add learning objectives if available
      if (syllabusData.learningObjectives && syllabusData.learningObjectives.length > 0) {
        learningObjectivesContent = `\n\nLEARNING OBJECTIVES:\n${syllabusData.learningObjectives.map(obj =>
          `- ${obj.objective} (Topic: ${obj.topic})`
        ).join('\n')}`;
      }

      // Add class-specific content
      classSpecificContent = `\n\nCLASS LEVEL FOCUS:\nThis question is specifically for Class ${className} (Standard ${className}) students. Ensure the content, vocabulary, and concepts are appropriate for students at this exact level, not mixing with other class levels.`;

      // Add PDF content (reduced to make room for structured data)
      if (syllabusData.extractedText) {
        syllabusContent = `\n\nSYLLABUS CONTENT:\n${syllabusData.extractedText.substring(0, 1000)}${syllabusData.extractedText.length > 1000 ? '...' : ''}`;
      }
    }

    return `Generate a multiple choice question for Tanzania ${level} education, Class ${className}, Subject: ${subject}.${competenciesContent}${learningObjectivesContent}${classSpecificContent}${syllabusContent}

CRITICAL REQUIREMENTS:
- Target Level: ${level} education
- Specific Class: Class ${className} (Standard ${className}) ONLY
- Subject: ${subject} ONLY - ABSOLUTELY NO OTHER SUBJECTS (not Mathematics, not English, not Geography, not History - ONLY ${subject})
- Difficulty: ${difficulty}
- Language Level: ${guidelines.languageLevel}
- Context: ${guidelines.contextualReferences}
- Cultural Sensitivity: ${guidelines.culturalSensitivity}
- Assessment Focus: ${guidelines.assessmentFocus}

PEDAGOGICAL APPROACH & TEACHING METHODS:
Consider these teaching and learning methods when designing the question:
- Inquiry-based learning: Encourage critical thinking and exploration
- Problem-solving approach: Present real-world scenarios requiring analysis
- Experiential learning: Connect to hands-on activities and practical experiences
- Discovery learning: Guide students to find answers through reasoning
- Collaborative learning: Include scenarios that could involve group work
- Constructivist approach: Build on students' prior knowledge and experiences

COMPREHENSIVE SYLLABUS ANALYSIS REQUIRED:
- MUST review ALL provided syllabus content before generating the question
- Ensure NO repetition of previously covered topics in this generation session
- Select from DIFFERENT sections of the syllabus to ensure comprehensive coverage
- Align with specific competencies and learning outcomes mentioned
- Consider the suggested teaching methods for each topic area

TOPIC DIVERSITY REQUIREMENT:
- If multiple topics are available in the syllabus, vary the topics across questions
- Do NOT focus on only one topic repeatedly
- Ensure questions cover different aspects of ${subject}
- Distribute questions across ALL major syllabus sections for comprehensive coverage

SYLLABUS TOPICS TO FOCUS ON: ${syllabusTopics.join(", ") || "Use diverse topics from the provided syllabus content"}

TOPIC REPETITION PREVENTION:
- This is part of a multi-question generation session
- MUST avoid repeating topics that have already been covered
- Select DIFFERENT syllabus sections and competencies for each question
- Ensure comprehensive coverage across the entire syllabus

STRICT GUIDELINES:
1. MUST align with the specific competencies and learning objectives provided above
2. MUST be appropriate for Class ${className} students specifically (not other classes)
3. MUST focus EXCLUSIVELY on ${subject} - ZERO tolerance for mixing subjects
4. MUST use vocabulary and concepts suitable for the exact class level
5. MUST follow Tanzania National Curriculum standards
6. MUST include relevant Tanzanian context and examples
7. MUST have exactly 4 options (A, B, C, D) with one clearly correct answer
8. MUST avoid repetitive or generic questions
9. MUST vary topics - do not focus on only one topic area
10. MUST ensure all content relates directly to ${subject} curriculum

FORBIDDEN CONTENT:
- Mathematics problems (unless this IS Mathematics)
- English language questions (unless this IS English)
- Geography questions (unless this IS Geography)
- History questions (unless this IS History)
- Any content from subjects other than ${subject}

Return ONLY valid JSON in this format:
{
  "question": "Question text that reflects appropriate teaching methods and real-world application",
  "options": {
    "A": "Option A text",
    "B": "Option B text",
    "C": "Option C text",
    "D": "Option D text"
  },
  "correctOption": "A",
  "syllabusTopics": ["specific_topic_from_syllabus"],
  "competencyAligned": "name_of_main_competency_this_question_addresses",
  "teachingMethod": "primary_teaching_method_reflected",
  "realWorldContext": "brief_description_of_practical_application",
  "classLevel": "${className}"
}`;
  }

  async buildFillBlankPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId = null) {
    const guidelines = questionGenerationGuidelines[level];
    const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);

    // Build enhanced prompt with structured syllabus content
    let syllabusContent = "";
    let competenciesContent = "";
    let learningObjectivesContent = "";
    let classSpecificContent = "";

    if ((syllabusData.source === 'pdf' || syllabusData.source === 'selected_pdf')) {
      // Add competencies if available
      if (syllabusData.competencies && syllabusData.competencies.length > 0) {
        competenciesContent = `\n\nMAIN COMPETENCIES:\n${syllabusData.competencies.map(comp =>
          `- ${comp.name}: ${comp.description}`
        ).join('\n')}`;
      }

      // Add learning objectives if available
      if (syllabusData.learningObjectives && syllabusData.learningObjectives.length > 0) {
        learningObjectivesContent = `\n\nLEARNING OBJECTIVES:\n${syllabusData.learningObjectives.map(obj =>
          `- ${obj.objective} (Topic: ${obj.topic})`
        ).join('\n')}`;
      }

      // Add class-specific content
      classSpecificContent = `\n\nCLASS LEVEL FOCUS:\nThis question is specifically for Class ${className} (Standard ${className}) students. Ensure the content and vocabulary are appropriate for this exact level.`;

      // Add PDF content (reduced)
      if (syllabusData.extractedText) {
        syllabusContent = `\n\nSYLLABUS CONTENT:\n${syllabusData.extractedText.substring(0, 800)}${syllabusData.extractedText.length > 800 ? '...' : ''}`;
      }
    }

    return `Generate a fill-in-the-blank question for Tanzania ${level} education, Class ${className}, Subject: ${subject}.${competenciesContent}${learningObjectivesContent}${classSpecificContent}${syllabusContent}

CRITICAL REQUIREMENTS:
- Target Level: ${level} education
- Specific Class: Class ${className} (Standard ${className}) ONLY
- Subject: ${subject} ONLY - DO NOT mix with other subjects
- Difficulty: ${difficulty}
- Language Level: ${guidelines.languageLevel}
- Context: ${guidelines.contextualReferences}
- Use underscores (____) for blanks
- Single word or short phrase answers

PEDAGOGICAL APPROACH & TEACHING METHODS:
Consider these teaching and learning methods when designing the question:
- Inquiry-based learning: Encourage critical thinking and exploration
- Problem-solving approach: Present real-world scenarios requiring analysis
- Experiential learning: Connect to hands-on activities and practical experiences
- Discovery learning: Guide students to find answers through reasoning
- Active learning: Engage students in the learning process

COMPREHENSIVE SYLLABUS ANALYSIS REQUIRED:
- MUST review ALL provided syllabus content before generating the question
- Ensure NO repetition of previously covered topics in this generation session
- Select from DIFFERENT sections of the syllabus to ensure comprehensive coverage
- Align with specific competencies and learning outcomes mentioned
- Consider the suggested teaching methods for each topic area

STRICT GUIDELINES:
1. MUST align with the specific competencies and learning objectives provided above
2. MUST be appropriate for Class ${className} students specifically
3. MUST focus ONLY on ${subject} - avoid mixing subjects
4. MUST use vocabulary suitable for the exact class level
5. MUST follow Tanzania National Curriculum standards
6. MUST avoid repetitive or generic questions
7. MUST reflect appropriate teaching/learning methods in the question design
8. MUST connect to real-world contexts and problem-solving scenarios

SYLLABUS TOPICS TO FOCUS ON: ${syllabusTopics.join(", ") || "Use diverse topics from the provided syllabus content"}

TOPIC REPETITION PREVENTION:
- This is part of a multi-question generation session
- MUST avoid repeating topics that have already been covered
- Select DIFFERENT syllabus sections and competencies for each question
- Ensure comprehensive coverage across the entire syllabus

Return ONLY valid JSON in this format:
{
  "question": "Question with _____ that reflects appropriate teaching methods and real-world application",
  "correctAnswer": "Expected answer",
  "syllabusTopics": ["specific_topic_from_syllabus"],
  "competencyAligned": "name_of_main_competency_this_question_addresses",
  "teachingMethod": "primary_teaching_method_reflected",
  "realWorldContext": "brief_description_of_practical_application",
  "classLevel": "${className}"
}`;
  }

  async buildPictureBasedPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId = null) {
    const guidelines = questionGenerationGuidelines[level];
    const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);

    // Build enhanced prompt with structured syllabus content
    let syllabusContent = "";
    let competenciesContent = "";
    let learningObjectivesContent = "";
    let classSpecificContent = "";

    if ((syllabusData.source === 'pdf' || syllabusData.source === 'selected_pdf')) {
      // Add competencies if available
      if (syllabusData.competencies && syllabusData.competencies.length > 0) {
        competenciesContent = `\n\nMAIN COMPETENCIES:\n${syllabusData.competencies.map(comp =>
          `- ${comp.name}: ${comp.description}`
        ).join('\n')}`;
      }

      // Add learning objectives if available
      if (syllabusData.learningObjectives && syllabusData.learningObjectives.length > 0) {
        learningObjectivesContent = `\n\nLEARNING OBJECTIVES:\n${syllabusData.learningObjectives.map(obj =>
          `- ${obj.objective} (Topic: ${obj.topic})`
        ).join('\n')}`;
      }

      // Add class-specific content
      classSpecificContent = `\n\nCLASS LEVEL FOCUS:\nThis question is specifically for Class ${className} (Standard ${className}) students. Ensure the visual content and concepts are appropriate for this exact level.`;

      // Add PDF content (reduced)
      if (syllabusData.extractedText) {
        syllabusContent = `\n\nSYLLABUS CONTENT:\n${syllabusData.extractedText.substring(0, 800)}${syllabusData.extractedText.length > 800 ? '...' : ''}`;
      }
    }

    return `Generate a picture-based multiple choice question for Tanzania ${level} education, Class ${className}, Subject: ${subject}.${competenciesContent}${learningObjectivesContent}${classSpecificContent}${syllabusContent}

CRITICAL REQUIREMENTS:
- Target Level: ${level} education
- Specific Class: Class ${className} (Standard ${className}) ONLY
- Subject: ${subject} ONLY - DO NOT mix with other subjects
- Difficulty: ${difficulty}
- Question should reference an image/diagram
- Include description of what image should show
- 4 options (A, B, C, D)

PEDAGOGICAL APPROACH & TEACHING METHODS:
Consider these teaching and learning methods when designing the visual question:
- Visual learning: Use diagrams, charts, and images to enhance understanding
- Inquiry-based learning: Encourage observation and analysis of visual elements
- Problem-solving approach: Present visual scenarios requiring interpretation
- Experiential learning: Connect to hands-on activities and practical experiences
- Discovery learning: Guide students to find answers through visual observation

COMPREHENSIVE SYLLABUS ANALYSIS REQUIRED:
- MUST review ALL provided syllabus content before generating the question
- Ensure NO repetition of previously covered topics in this generation session
- Select from DIFFERENT sections of the syllabus to ensure comprehensive coverage
- Align with specific competencies and learning outcomes mentioned
- Consider the suggested teaching methods for each topic area

STRICT GUIDELINES:
1. MUST align with the specific competencies and learning objectives provided above
2. MUST be appropriate for Class ${className} students specifically
3. MUST focus ONLY on ${subject} - avoid mixing subjects
4. MUST use visual concepts suitable for the exact class level
5. MUST follow Tanzania National Curriculum standards
6. MUST avoid repetitive or generic questions
7. MUST reflect appropriate teaching/learning methods in the visual question design
8. MUST connect to real-world contexts and practical applications

SYLLABUS TOPICS TO FOCUS ON: ${syllabusTopics.join(", ") || "Use diverse topics from the provided syllabus content"}

TOPIC REPETITION PREVENTION:
- This is part of a multi-question generation session
- MUST avoid repeating topics that have already been covered
- Select DIFFERENT syllabus sections and competencies for each question
- Ensure comprehensive coverage across the entire syllabus

Return ONLY valid JSON in this format:
{
  "question": "Visual question that reflects appropriate teaching methods and real-world application",
  "options": {
    "A": "Option A text",
    "B": "Option B text",
    "C": "Option C text",
    "D": "Option D text"
  },
  "correctOption": "A",
  "imageDescription": "Detailed description of the visual content that supports the teaching method",
  "syllabusTopics": ["specific_topic_from_syllabus"],
  "competencyAligned": "name_of_main_competency_this_question_addresses",
  "teachingMethod": "primary_teaching_method_reflected",
  "realWorldContext": "brief_description_of_practical_application",
  "classLevel": "${className}"
}`;
  }

  // Get syllabus data for specific level/class/subject
  async getSyllabusData(level, className, subject, selectedSyllabusId = null) {
    try {
      // If a specific syllabus is selected, use it directly
      if (selectedSyllabusId) {
        console.log(`🎯 Using selected syllabus: ${selectedSyllabusId}`);
        const Syllabus = require("../models/syllabusModel");
        const selectedSyllabus = await Syllabus.findById(selectedSyllabusId);

        if (selectedSyllabus && selectedSyllabus.processingStatus === 'completed') {
          console.log(`📚 Using selected syllabus: ${selectedSyllabus.title}`);
          return {
            topics: selectedSyllabus.getTopicsForAI(),
            extractedText: selectedSyllabus.extractedText,
            learningObjectives: selectedSyllabus.learningObjectives,
            competencies: selectedSyllabus.competencies,
            source: 'selected_pdf',
            syllabusId: selectedSyllabus._id,
            syllabusTitle: selectedSyllabus.title,
          };
        } else {
          console.warn(`⚠️ Selected syllabus ${selectedSyllabusId} not found or not completed`);
        }
      }

      // First try to get PDF-based syllabus content
      const pdfSyllabus = await SyllabusService.getSyllabusForAI(level, className, subject);

      if (pdfSyllabus && pdfSyllabus.topics && Object.keys(pdfSyllabus.topics).length > 0) {
        console.log(`📚 Using PDF syllabus for ${level} ${subject} Class ${className}`);
        return {
          topics: pdfSyllabus.topics,
          extractedText: pdfSyllabus.extractedText,
          learningObjectives: pdfSyllabus.learningObjectives,
          competencies: pdfSyllabus.competencies,
          source: 'pdf',
          syllabusId: pdfSyllabus.syllabusId,
        };
      }

      // Fallback to hardcoded syllabus data
      console.log(`📖 Using hardcoded syllabus for ${level} ${subject} Class ${className}`);
      let syllabusSource;
      switch (level) {
        case "primary":
          syllabusSource = primarySyllabus;
          break;
        case "secondary":
          syllabusSource = secondarySyllabus;
          break;
        case "advance":
          syllabusSource = advanceSyllabus;
          break;
        default:
          return { source: 'none' };
      }

      const hardcodedData = syllabusSource[subject]?.[className] || {};
      return {
        ...hardcodedData,
        source: 'hardcoded',
      };

    } catch (error) {
      console.error("Error getting syllabus data:", error);
      // Return empty object as fallback
      return { source: 'error', error: error.message };
    }
  }

  // Call OpenAI API
  async callOpenAI(prompt) {
    // Check if API key is available
    if (!this.openaiApiKey) {
      throw new Error("OpenAI API key is not configured. Please check your environment variables.");
    }

    console.log("🤖 Making OpenAI API call...");
    const startTime = Date.now();

    try {
      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: this.model,
          messages: [
            {
              role: "system",
              content: "You are an expert in Tanzania education curriculum and question generation. Always return valid JSON responses without markdown formatting."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.openaiApiKey}`,
          },
          timeout: 60000, // 60 seconds timeout for individual OpenAI calls
        }
      );

      const endTime = Date.now();
      console.log(`✅ OpenAI API call completed in ${endTime - startTime}ms`);
      return response;
    } catch (error) {
      const endTime = Date.now();
      console.error(`❌ OpenAI API call failed after ${endTime - startTime}ms:`, error.message);

      if (error.code === 'ECONNABORTED') {
        throw new Error("OpenAI API request timed out. Please try again.");
      }

      throw error;
    }

    let content = response.data.choices[0].message.content.trim();

    // Clean up markdown code blocks if present
    content = content.replace(/```json\s*/g, '').replace(/```\s*$/g, '');

    return content;
  }

  // Helper method to safely parse JSON response
  parseJSONResponse(response) {
    try {
      // Extract the content from OpenAI response
      let content;
      if (response.data && response.data.choices && response.data.choices[0]) {
        content = response.data.choices[0].message.content;
      } else if (typeof response === 'string') {
        content = response;
      } else {
        throw new Error('Invalid response format from OpenAI API');
      }

      // Additional cleanup for any remaining markdown or formatting
      let cleanResponse = content.trim();

      // Remove any leading/trailing backticks or code block markers
      cleanResponse = cleanResponse.replace(/^```(?:json)?\s*/, '');
      cleanResponse = cleanResponse.replace(/\s*```$/, '');

      // Parse the JSON
      const parsed = JSON.parse(cleanResponse);
      console.log('✅ Successfully parsed OpenAI response');
      return parsed;
    } catch (error) {
      console.error('JSON parsing error:', error.message);
      console.error('Raw response type:', typeof response);
      console.error('Raw response:', response);
      throw new Error(`Failed to parse AI response as JSON: ${error.message}`);
    }
  }
}

module.exports = AIQuestionGenerationService;
