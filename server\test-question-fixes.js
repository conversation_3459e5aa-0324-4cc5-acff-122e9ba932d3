require('dotenv').config();
const mongoose = require('mongoose');
const Question = require('./models/questionModel');
const Exam = require('./models/examModel');

async function testQuestionFixes() {
  try {
    console.log('🔍 Testing question fixes...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find a test exam
    const exam = await Exam.findOne().populate('questions');
    if (!exam) {
      console.log('❌ No exams found in database');
      return;
    }
    
    console.log(`📋 Testing with exam: "${exam.name}"`);
    console.log(`📊 Exam has ${exam.questions.length} questions`);
    
    if (exam.questions.length > 0) {
      const firstQuestion = exam.questions[0];
      console.log('\n📝 First question details:');
      console.log('- Name:', firstQuestion.name);
      console.log('- Type:', firstQuestion.type);
      console.log('- Correct Answer:', firstQuestion.correctAnswer);
      console.log('- Generation Source:', firstQuestion.generationSource);
      console.log('- Is AI Generated:', firstQuestion.isAIGenerated);
      
      if (firstQuestion.options) {
        console.log('- Options:', Object.keys(firstQuestion.options).length, 'options');
      }
    }
    
    // Test creating a PDF-extracted question
    console.log('\n🧪 Testing PDF extraction question creation...');
    
    const testQuestion = new Question({
      name: "Test PDF extracted question: What is 2 + 2?",
      type: "mcq",
      options: {
        A: "3",
        B: "4", 
        C: "5",
        D: "6"
      },
      correctAnswer: "B",
      topic: "Mathematics",
      classLevel: "5",
      exam: exam._id,
      isAIGenerated: true,
      generationSource: "pdf_extraction",
      extractionConfidence: 0.95
    });
    
    const savedQuestion = await testQuestion.save();
    console.log('✅ Test question saved with ID:', savedQuestion._id);
    
    // Add to exam
    exam.questions.push(savedQuestion._id);
    await exam.save();
    console.log('✅ Question added to exam');
    
    // Verify by re-fetching
    const updatedExam = await Exam.findById(exam._id).populate('questions');
    console.log(`✅ Verification: Exam now has ${updatedExam.questions.length} questions`);
    
    // Clean up test question
    await Question.findByIdAndDelete(savedQuestion._id);
    exam.questions.pull(savedQuestion._id);
    await exam.save();
    console.log('🧹 Test question cleaned up');
    
    console.log('\n🎉 All tests passed! PDF extraction should work correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

testQuestionFixes();
