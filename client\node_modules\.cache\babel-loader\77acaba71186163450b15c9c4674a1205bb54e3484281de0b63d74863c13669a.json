{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Login\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input } from \"antd\";\nimport React from \"react\";\nimport './index.css';\nimport Logo from '../../../assets/logo.png';\nimport { useDispatch } from \"react-redux\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { loginUser } from \"../../../apicalls/users\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { SetUser } from \"../../../redux/usersSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await loginUser(values);\n      dispatch(HideLoading());\n      console.log('Login response:', response);\n      if (response.success) {\n        var _response$response;\n        message.success(response.message);\n        localStorage.setItem(\"token\", response.data);\n\n        // Store user data in localStorage for consistency\n        if (response.response) {\n          localStorage.setItem(\"user\", JSON.stringify(response.response));\n\n          // IMPORTANT: Set user data in Redux immediately to prevent redirect issues\n          dispatch(SetUser(response.response));\n        }\n\n        // Check admin status from response.response (user object)\n        if ((_response$response = response.response) !== null && _response$response !== void 0 && _response$response.isAdmin) {\n          console.log(\"Admin user detected, redirecting to admin dashboard\");\n          navigate(\"/admin/dashboard\");\n        } else {\n          console.log(\"Regular user detected, redirecting to user hub\");\n          navigate(\"/user/hub\");\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Login error:', error);\n      message.error(\"Login failed. Please try again.\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: Logo,\n          alt: \"BrainWave Logo\",\n          className: \"login-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"login-title\",\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login-subtitle\",\n          children: \"Sign in to your account to continue learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"vertical\",\n        onFinish: onFinish,\n        className: \"login-form\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"Email\",\n          rules: [{\n            required: true,\n            message: \"Please input your email!\"\n          }, {\n            type: \"email\",\n            message: \"Please enter a valid email!\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            className: \"form-input\",\n            placeholder: \"Enter your email\",\n            autoComplete: \"email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"Password\",\n          rules: [{\n            required: true,\n            message: \"Please input your password!\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            className: \"form-input\",\n            placeholder: \"Enter your password\",\n            autoComplete: \"current-password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"login-btn\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"login-link\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["Form", "message", "Input", "React", "Logo", "useDispatch", "Link", "useNavigate", "loginUser", "HideLoading", "ShowLoading", "SetUser", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "dispatch", "onFinish", "values", "response", "console", "log", "success", "_response$response", "localStorage", "setItem", "data", "JSON", "stringify", "isAdmin", "error", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "type", "placeholder", "autoComplete", "Password", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Login/index.js"], "sourcesContent": ["import { Form, message, Input } from \"antd\";\r\nimport React from \"react\";\r\nimport './index.css';\r\nimport Logo from '../../../assets/logo.png';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { loginUser } from \"../../../apicalls/users\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { SetUser } from \"../../../redux/usersSlice\";\r\n\r\nfunction Login() {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await loginUser(values);\r\n      dispatch(HideLoading());\r\n\r\n      console.log('Login response:', response);\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        localStorage.setItem(\"token\", response.data);\r\n\r\n        // Store user data in localStorage for consistency\r\n        if (response.response) {\r\n          localStorage.setItem(\"user\", JSON.stringify(response.response));\r\n\r\n          // IMPORTANT: Set user data in Redux immediately to prevent redirect issues\r\n          dispatch(SetUser(response.response));\r\n        }\r\n\r\n        // Check admin status from response.response (user object)\r\n        if (response.response?.isAdmin) {\r\n          console.log(\"Admin user detected, redirecting to admin dashboard\");\r\n          navigate(\"/admin/dashboard\");\r\n        } else {\r\n          console.log(\"Regular user detected, redirecting to user hub\");\r\n          navigate(\"/user/hub\");\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Login error:', error);\r\n      message.error(\"Login failed. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login-container\">\r\n      <div className=\"login-card\">\r\n        <div className=\"login-header\">\r\n          <img src={Logo} alt=\"BrainWave Logo\" className=\"login-logo\" />\r\n          <h1 className=\"login-title\">Welcome Back</h1>\r\n          <p className=\"login-subtitle\">Sign in to your account to continue learning</p>\r\n        </div>\r\n\r\n        <Form layout=\"vertical\" onFinish={onFinish} className=\"login-form\">\r\n          <Form.Item\r\n            name=\"email\"\r\n            label=\"Email\"\r\n            rules={[\r\n              { required: true, message: \"Please input your email!\" },\r\n              { type: \"email\", message: \"Please enter a valid email!\" }\r\n            ]}\r\n          >\r\n            <Input\r\n              type=\"email\"\r\n              className=\"form-input\"\r\n              placeholder=\"Enter your email\"\r\n              autoComplete=\"email\"\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"password\"\r\n            label=\"Password\"\r\n            rules={[{ required: true, message: \"Please input your password!\" }]}\r\n          >\r\n            <Input.Password\r\n              className=\"form-input\"\r\n              placeholder=\"Enter your password\"\r\n              autoComplete=\"current-password\"\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item>\r\n            <button type=\"submit\" className=\"login-btn\">\r\n              Sign In\r\n            </button>\r\n          </Form.Item>\r\n        </Form>\r\n\r\n        <div className=\"login-footer\">\r\n          <p>\r\n            Don't have an account?{' '}\r\n            <Link to=\"/register\" className=\"login-link\">\r\n              Create Account\r\n            </Link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAMa,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACFF,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMU,QAAQ,GAAG,MAAMZ,SAAS,CAACW,MAAM,CAAC;MACxCF,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;MAEvBY,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACG,OAAO,EAAE;QAAA,IAAAC,kBAAA;QACpBvB,OAAO,CAACsB,OAAO,CAACH,QAAQ,CAACnB,OAAO,CAAC;QACjCwB,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEN,QAAQ,CAACO,IAAI,CAAC;;QAE5C;QACA,IAAIP,QAAQ,CAACA,QAAQ,EAAE;UACrBK,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACT,QAAQ,CAACA,QAAQ,CAAC,CAAC;;UAE/D;UACAH,QAAQ,CAACN,OAAO,CAACS,QAAQ,CAACA,QAAQ,CAAC,CAAC;QACtC;;QAEA;QACA,KAAAI,kBAAA,GAAIJ,QAAQ,CAACA,QAAQ,cAAAI,kBAAA,eAAjBA,kBAAA,CAAmBM,OAAO,EAAE;UAC9BT,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClEN,QAAQ,CAAC,kBAAkB,CAAC;QAC9B,CAAC,MAAM;UACLK,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7DN,QAAQ,CAAC,WAAW,CAAC;QACvB;MACF,CAAC,MAAM;QACLf,OAAO,CAAC8B,KAAK,CAACX,QAAQ,CAACnB,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdd,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;MACvBY,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC9B,OAAO,CAAC8B,KAAK,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC;EAED,oBACElB,OAAA;IAAKmB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BpB,OAAA;MAAKmB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBpB,OAAA;QAAKmB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpB,OAAA;UAAKqB,GAAG,EAAE9B,IAAK;UAAC+B,GAAG,EAAC,gBAAgB;UAACH,SAAS,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9D1B,OAAA;UAAImB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C1B,OAAA;UAAGmB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAA4C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAEN1B,OAAA,CAACb,IAAI;QAACwC,MAAM,EAAC,UAAU;QAACtB,QAAQ,EAAEA,QAAS;QAACc,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAChEpB,OAAA,CAACb,IAAI,CAACyC,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,OAAO;UACbC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE5C,OAAO,EAAE;UAA2B,CAAC,EACvD;YAAE6C,IAAI,EAAE,OAAO;YAAE7C,OAAO,EAAE;UAA8B,CAAC,CACzD;UAAAgC,QAAA,eAEFpB,OAAA,CAACX,KAAK;YACJ4C,IAAI,EAAC,OAAO;YACZd,SAAS,EAAC,YAAY;YACtBe,WAAW,EAAC,kBAAkB;YAC9BC,YAAY,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1B,OAAA,CAACb,IAAI,CAACyC,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,UAAU;UAChBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5C,OAAO,EAAE;UAA8B,CAAC,CAAE;UAAAgC,QAAA,eAEpEpB,OAAA,CAACX,KAAK,CAAC+C,QAAQ;YACbjB,SAAS,EAAC,YAAY;YACtBe,WAAW,EAAC,qBAAqB;YACjCC,YAAY,EAAC;UAAkB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1B,OAAA,CAACb,IAAI,CAACyC,IAAI;UAAAR,QAAA,eACRpB,OAAA;YAAQiC,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP1B,OAAA;QAAKmB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BpB,OAAA;UAAAoB,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1BpB,OAAA,CAACP,IAAI;YAAC4C,EAAE,EAAC,WAAW;YAAClB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxB,EAAA,CAlGQD,KAAK;EAAA,QACKP,WAAW,EACXF,WAAW;AAAA;AAAA8C,EAAA,GAFrBrC,KAAK;AAoGd,eAAeA,KAAK;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}