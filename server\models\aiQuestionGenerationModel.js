const mongoose = require("mongoose");

// Schema for storing AI question generation requests and history
const aiQuestionGenerationSchema = new mongoose.Schema(
  {
    // Generation Request Details
    requestedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    examId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "exams",
      required: false, // Allow standalone question generation without exam
    },
    
    // Generation Parameters
    generationParams: {
      questionTypes: [{
        type: String,
        enum: ["multiple_choice", "fill_blank", "picture_based"],
        required: true,
      }],
      subjects: [{
        type: String,
        required: true,
      }],
      level: {
        type: String,
        enum: ["primary", "secondary", "advance"],
        required: true,
      },
      class: {
        type: String,
        required: true,
      },
      difficultyLevels: [{
        type: String,
        enum: ["easy", "medium", "hard"],
      }],
      syllabusTopics: [{
        type: String,
      }],
      totalQuestions: {
        type: Number,
        required: true,
        min: 1,
        max: 50,
      },
      questionDistribution: {
        multiple_choice: { type: Number, default: 0 },
        fill_blank: { type: Number, default: 0 },
        picture_based: { type: Number, default: 0 },
      },
    },

    // Generation Results
    generationStatus: {
      type: String,
      enum: ["pending", "in_progress", "completed", "failed", "cancelled"],
      default: "pending",
    },
    generatedQuestions: [{
      questionId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "questions",
      },
      generatedContent: mongoose.Schema.Types.Mixed, // Use Mixed type to avoid casting issues
      approved: {
        type: Boolean,
        default: false,
      },
      rejectionReason: String,
      editedContent: Object,
    }],
    
    // Generation Metadata
    aiModel: {
      type: String,
      default: "gpt-4o",
    },
    generationTime: {
      type: Number, // in milliseconds
    },
    errorMessage: String,
    retryCount: {
      type: Number,
      default: 0,
    },
    
    // Quality Control
    qualityScore: {
      type: Number,
      min: 0,
      max: 100,
    },
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
    },
    reviewNotes: String,
    
    // Tanzania Syllabus Compliance
    syllabusCompliance: {
      verified: {
        type: Boolean,
        default: false,
      },
      verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "users",
      },
      complianceNotes: String,
    },
  },
  {
    timestamps: true,
  }
);

// Schema for storing Tanzania syllabus topics and structure
const tanzaniaSyllabusSchema = new mongoose.Schema(
  {
    level: {
      type: String,
      enum: ["primary", "secondary", "advance"],
      required: true,
    },
    class: {
      type: String,
      required: true,
    },
    subject: {
      type: String,
      required: true,
    },
    topics: [{
      topicName: {
        type: String,
        required: true,
      },
      subtopics: [{
        name: String,
        description: String,
        learningObjectives: [String],
        keyTerms: [String],
      }],
      difficulty: {
        type: String,
        enum: ["easy", "medium", "hard"],
        default: "medium",
      },
      estimatedHours: Number,
    }],
    competencies: [{
      name: String,
      description: String,
      assessmentCriteria: [String],
    }],
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Schema for storing question generation templates
const questionTemplateSchema = new mongoose.Schema(
  {
    templateName: {
      type: String,
      required: true,
    },
    questionType: {
      type: String,
      enum: ["multiple_choice", "fill_blank", "picture_based"],
      required: true,
    },
    subject: {
      type: String,
      required: true,
    },
    level: {
      type: String,
      enum: ["primary", "secondary", "advance", "Primary", "Secondary", "Advance"],
      required: true,
    },
    template: {
      prompt: {
        type: String,
        required: true,
      },
      structure: Object,
      examples: [Object],
      constraints: [String],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    usageCount: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

const AIQuestionGeneration = mongoose.model("ai-question-generations", aiQuestionGenerationSchema);
const TanzaniaSyllabus = mongoose.model("tanzania-syllabus", tanzaniaSyllabusSchema);
const QuestionTemplate = mongoose.model("question-templates", questionTemplateSchema);

module.exports = {
  AIQuestionGeneration,
  TanzaniaSyllabus,
  QuestionTemplate,
};
