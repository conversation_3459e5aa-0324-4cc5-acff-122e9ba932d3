require('dotenv').config();
const mongoose = require('mongoose');
const Question = require('./models/questionModel');
const Exam = require('./models/examModel');

async function simpleExamDebug() {
  try {
    console.log('🔍 Simple exam debug...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find all exams
    const exams = await Exam.find();
    console.log(`📋 Found ${exams.length} exams (without populate):\n`);
    
    for (const exam of exams) {
      console.log(`📚 Exam: "${exam.name}" (ID: ${exam._id})`);
      console.log(`   - Questions array length: ${exam.questions.length}`);
      console.log(`   - Questions IDs: [${exam.questions.join(', ')}]`);
      
      // Now populate and check
      const populatedExam = await Exam.findById(exam._id).populate('questions');
      console.log(`   - After populate: ${populatedExam.questions.length} questions`);
      
      if (populatedExam.questions.length > 0) {
        console.log(`   - First question: "${populatedExam.questions[0].name?.substring(0, 50)}..."`);
      }
      console.log('');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

simpleExamDebug();
