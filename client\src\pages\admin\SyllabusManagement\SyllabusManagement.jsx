import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Upload, 
  message, 
  Tag, 
  Space, 
  Tooltip,
  Progress,
  Popconfirm,
  Row,
  Col,
  Statistic
} from 'antd';
import { 
  TbUpload, 
  TbEye, 
  TbEdit, 
  TbTrash, 
  TbDownload,
  TbFileText,
  TbBook,
  TbUsers,
  TbClock,
  TbCheck,
  TbX,
  TbRefresh,
  TbPlus
} from 'react-icons/tb';
import { useDispatch } from 'react-redux';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import {
  getAllSyllabuses,
  uploadSyllabus,
  deleteSyllabus,
  updateSyllabus,
  getSyllabusById,
  approveSyllabus
} from '../../../apicalls/syllabus';
import { primarySubjects, secondarySubjects, advanceSubjects } from '../../../data/Subjects';


const { Option } = Select;
const { TextArea } = Input;

const SyllabusManagement = () => {
  const [syllabuses, setSyllabuses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [selectedSyllabus, setSelectedSyllabus] = useState(null);
  const [filters, setFilters] = useState({});
  const [selectedLevel, setSelectedLevel] = useState('');
  const [availableSubjects, setAvailableSubjects] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    failed: 0
  });
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  // Get subjects based on selected level
  const getSubjectsForLevel = (level) => {
    switch (level) {
      case 'primary':
        return primarySubjects;
      case 'secondary':
        return secondarySubjects;
      case 'advance':
        return advanceSubjects;
      default:
        return [];
    }
  };

  // Handle level change in upload form
  const handleLevelChange = (level) => {
    setSelectedLevel(level);
    const subjects = getSubjectsForLevel(level);
    setAvailableSubjects(subjects);
    // Reset subject field when level changes
    form.setFieldsValue({ subject: undefined });
  };

  useEffect(() => {
    fetchSyllabuses();
  }, [filters]);

  const fetchSyllabuses = async () => {
    try {
      setLoading(true);
      const response = await getAllSyllabuses(filters);
      if (response.success) {
        setSyllabuses(response.data);
        calculateStats(response.data);
      } else {
        message.error('Failed to fetch syllabuses');
      }
    } catch (error) {
      message.error('Error fetching syllabuses');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data) => {
    const stats = {
      total: data.length,
      pending: data.filter(s => s.processingStatus === 'pending').length,
      completed: data.filter(s => s.processingStatus === 'completed').length,
      failed: data.filter(s => s.processingStatus === 'failed').length,
    };
    setStats(stats);
  };

  const handleUpload = async (values) => {
    try {
      dispatch(ShowLoading());
      
      const formData = new FormData();
      formData.append('syllabusFile', values.file.file);
      formData.append('title', values.title);
      formData.append('description', values.description || '');
      formData.append('level', values.level);
      formData.append('classes', Array.isArray(values.classes) ? values.classes.join(',') : values.classes);
      formData.append('subject', values.subject);
      formData.append('academicYear', values.academicYear || '');
      formData.append('tags', values.tags || '');

      const response = await uploadSyllabus(formData);
      
      if (response.success) {
        message.success('Syllabus uploaded successfully! Processing will begin shortly.');
        setUploadModalVisible(false);
        form.resetFields();
        fetchSyllabuses();
      } else {
        message.error(response.message || 'Failed to upload syllabus');
      }
    } catch (error) {
      message.error('Error uploading syllabus');
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleDelete = async (id) => {
    try {
      dispatch(ShowLoading());
      const response = await deleteSyllabus(id);
      
      if (response.success) {
        message.success('Syllabus deleted successfully');
        fetchSyllabuses();
      } else {
        message.error(response.message || 'Failed to delete syllabus');
      }
    } catch (error) {
      message.error('Error deleting syllabus');
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleView = async (id) => {
    try {
      dispatch(ShowLoading());
      const response = await getSyllabusById(id);
      
      if (response.success) {
        setSelectedSyllabus(response.data);
        setViewModalVisible(true);
      } else {
        message.error('Failed to fetch syllabus details');
      }
    } catch (error) {
      message.error('Error fetching syllabus details');
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleApprove = async (id) => {
    try {
      dispatch(ShowLoading());
      const response = await approveSyllabus(id, { isActive: true });
      
      if (response.success) {
        message.success('Syllabus approved successfully');
        fetchSyllabuses();
      } else {
        message.error('Failed to approve syllabus');
      }
    } catch (error) {
      message.error('Error approving syllabus');
    } finally {
      dispatch(HideLoading());
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'green';
      case 'processing': return 'blue';
      case 'pending': return 'orange';
      case 'failed': return 'red';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <TbCheck />;
      case 'processing': return <TbClock />;
      case 'pending': return <TbClock />;
      case 'failed': return <TbX />;
      default: return <TbClock />;
    }
  };

  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">
            {record.level} • Classes {Array.isArray(record.classes) ? record.classes.join(', ') : record.classes} • {record.subject}
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'processingStatus',
      key: 'status',
      render: (status) => (
        <Tag 
          color={getStatusColor(status)} 
          icon={getStatusIcon(status)}
        >
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Quality Score',
      dataIndex: 'qualityScore',
      key: 'qualityScore',
      render: (score) => (
        score ? (
          <Progress 
            percent={score} 
            size="small" 
            status={score >= 80 ? 'success' : score >= 60 ? 'normal' : 'exception'}
          />
        ) : '-'
      ),
    },
    {
      title: 'Uploaded',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<TbEye />} 
              onClick={() => handleView(record._id)}
            />
          </Tooltip>
          
          {record.processingStatus === 'completed' && !record.approvedBy && (
            <Tooltip title="Approve">
              <Button 
                type="text" 
                icon={<TbCheck />} 
                onClick={() => handleApprove(record._id)}
                style={{ color: 'green' }}
              />
            </Tooltip>
          )}
          
          <Tooltip title="Download">
            <Button 
              type="text" 
              icon={<TbDownload />} 
              onClick={() => window.open(`/api/syllabus/${record._id}/download`)}
            />
          </Tooltip>
          
          <Popconfirm
            title="Are you sure you want to delete this syllabus?"
            onConfirm={() => handleDelete(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button 
                type="text" 
                icon={<TbTrash />} 
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="syllabus-management">
      <div className="page-header">
        <div>
          <h1 className="page-title">
            <TbBook className="title-icon" />
            Syllabus Management
          </h1>
          <p className="page-description">
            Upload and manage syllabus PDFs for AI question generation
          </p>
        </div>
        <Button 
          type="primary" 
          icon={<TbPlus />}
          onClick={() => setUploadModalVisible(true)}
          size="large"
        >
          Upload Syllabus
        </Button>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} className="stats-row">
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Syllabuses"
              value={stats.total}
              prefix={<TbFileText />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Completed"
              value={stats.completed}
              prefix={<TbCheck />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Processing"
              value={stats.pending}
              prefix={<TbClock />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Failed"
              value={stats.failed}
              prefix={<TbX />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card className="filters-card">
        <Row gutter={16}>
          <Col span={6}>
            <Select
              placeholder="Filter by Level"
              allowClear
              onChange={(value) => setFilters({ ...filters, level: value })}
              style={{ width: '100%' }}
            >
              <Option value="primary">Primary</Option>
              <Option value="secondary">Secondary</Option>
              <Option value="advance">Advance</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="Filter by Status"
              allowClear
              onChange={(value) => setFilters({ ...filters, processingStatus: value })}
              style={{ width: '100%' }}
            >
              <Option value="pending">Pending</Option>
              <Option value="processing">Processing</Option>
              <Option value="completed">Completed</Option>
              <Option value="failed">Failed</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Input
              placeholder="Search by subject"
              allowClear
              onChange={(e) => setFilters({ ...filters, subject: e.target.value })}
            />
          </Col>
          <Col span={6}>
            <Button 
              icon={<TbRefresh />}
              onClick={fetchSyllabuses}
            >
              Refresh
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Syllabuses Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={syllabuses}
          rowKey="_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} syllabuses`,
          }}
        />
      </Card>

      {/* Upload Modal */}
      <Modal
        title="Upload New Syllabus"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setSelectedLevel('');
          setAvailableSubjects([]);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
        >
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter syllabus title' }]}
          >
            <Input placeholder="e.g., Mathematics Primary Classes 5-7 Syllabus 2024" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea 
              rows={3} 
              placeholder="Brief description of the syllabus content"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="level"
                label="Level"
                rules={[{ required: true, message: 'Please select level' }]}
              >
                <Select
                  placeholder="Select level"
                  onChange={handleLevelChange}
                >
                  <Option value="primary">Primary</Option>
                  <Option value="secondary">Secondary</Option>
                  <Option value="advance">Advance</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="classes"
                label="Classes (can select multiple)"
                rules={[{ required: true, message: 'Please select at least one class' }]}
                extra="One syllabus can be shared across multiple classes (e.g., Classes 5, 6, 7)"
              >
                <Select
                  mode="multiple"
                  placeholder="Select classes that will use this syllabus"
                  allowClear
                >
                  <Option value="1">Class 1</Option>
                  <Option value="2">Class 2</Option>
                  <Option value="3">Class 3</Option>
                  <Option value="4">Class 4</Option>
                  <Option value="5">Class 5</Option>
                  <Option value="6">Class 6</Option>
                  <Option value="7">Class 7</Option>
                  <Option value="I">Form I</Option>
                  <Option value="II">Form II</Option>
                  <Option value="III">Form III</Option>
                  <Option value="IV">Form IV</Option>
                  <Option value="V">Form V</Option>
                  <Option value="VI">Form VI</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="subject"
                label="Subject"
                rules={[{ required: true, message: 'Please select subject' }]}
                extra={selectedLevel ? `Choose from ${selectedLevel} level subjects` : 'Select level first'}
              >
                <Select
                  placeholder={selectedLevel ? "Select subject" : "Select level first"}
                  disabled={!selectedLevel}
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {availableSubjects.map((subject) => (
                    <Option key={subject} value={subject}>
                      {subject}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="academicYear"
                label="Academic Year"
              >
                <Input placeholder="e.g., 2024" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tags"
                label="Tags (comma-separated)"
              >
                <Input placeholder="e.g., official, updated, curriculum" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="file"
            label="Syllabus PDF File"
            rules={[{ required: true, message: 'Please upload a PDF file' }]}
          >
            <Upload.Dragger
              accept=".pdf"
              maxCount={1}
              beforeUpload={() => false}
            >
              <p className="ant-upload-drag-icon">
                <TbUpload style={{ fontSize: '48px' }} />
              </p>
              <p className="ant-upload-text">
                Click or drag PDF file to this area to upload
              </p>
              <p className="ant-upload-hint">
                Support for single PDF file upload. Maximum file size: 50MB
              </p>
            </Upload.Dragger>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Upload Syllabus
              </Button>
              <Button onClick={() => {
                setUploadModalVisible(false);
                setSelectedLevel('');
                setAvailableSubjects([]);
                form.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* View Modal */}
      <Modal
        title="Syllabus Details"
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedSyllabus && (
          <div className="syllabus-details">
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>Title:</strong> {selectedSyllabus.title}</p>
                <p><strong>Level:</strong> {selectedSyllabus.level}</p>
                <p><strong>Classes:</strong> {Array.isArray(selectedSyllabus.classes) ? selectedSyllabus.classes.join(', ') : selectedSyllabus.classes}</p>
                <p><strong>Subject:</strong> {selectedSyllabus.subject}</p>
              </Col>
              <Col span={12}>
                <p><strong>Status:</strong> 
                  <Tag color={getStatusColor(selectedSyllabus.processingStatus)}>
                    {selectedSyllabus.processingStatus}
                  </Tag>
                </p>
                <p><strong>Quality Score:</strong> {selectedSyllabus.qualityScore || 'N/A'}</p>
                <p><strong>File Size:</strong> {(selectedSyllabus.fileSize / 1024 / 1024).toFixed(2)} MB</p>
                <p><strong>Uploaded:</strong> {new Date(selectedSyllabus.createdAt).toLocaleString()}</p>
              </Col>
            </Row>
            
            {selectedSyllabus.description && (
              <div>
                <h4>Description:</h4>
                <p>{selectedSyllabus.description}</p>
              </div>
            )}
            
            {selectedSyllabus.extractedTopics && selectedSyllabus.extractedTopics.length > 0 && (
              <div>
                <h4>Extracted Topics:</h4>
                <div className="topics-list">
                  {selectedSyllabus.extractedTopics.slice(0, 10).map((topic, index) => (
                    <Tag key={index} className="topic-tag">
                      {topic.topicName}
                    </Tag>
                  ))}
                  {selectedSyllabus.extractedTopics.length > 10 && (
                    <Tag>+{selectedSyllabus.extractedTopics.length - 10} more</Tag>
                  )}
                </div>
              </div>
            )}
            
            {selectedSyllabus.processingError && (
              <div>
                <h4>Processing Error:</h4>
                <p style={{ color: 'red' }}>{selectedSyllabus.processingError}</p>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SyllabusManagement;
