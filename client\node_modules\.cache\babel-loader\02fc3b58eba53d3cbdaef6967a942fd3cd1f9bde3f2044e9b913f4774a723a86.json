{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\AdminProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n  useEffect(() => {\n    console.log(\"AdminProtectedRoute: User state changed\", {\n      user: user ? {\n        name: user.name,\n        isAdmin: user.isAdmin\n      } : null\n    });\n\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      console.log(\"AdminProtectedRoute: Non-admin user detected, redirecting to user hub\");\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    console.log(\"AdminProtectedRoute: User not loaded yet, showing loading...\");\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading admin panel...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    console.log(\"AdminProtectedRoute: User is not admin, will redirect\");\n    return null;\n  }\n  console.log(\"AdminProtectedRoute: Admin user confirmed, rendering children\");\n  // If user is admin, render the children\n  return children;\n};\n_s(AdminProtectedRoute, \"mL2t1COiLoZ5+Kl1K33h74o9/R8=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AdminProtectedRoute;\nexport default AdminProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminProtectedRoute\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useNavigate", "message", "jsxDEV", "_jsxDEV", "AdminProtectedRoute", "children", "_s", "user", "state", "navigate", "console", "log", "name", "isAdmin", "error", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/AdminProtectedRoute.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\n\nconst AdminProtectedRoute = ({ children }) => {\n  const { user } = useSelector((state) => state.user);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    console.log(\"AdminProtectedRoute: User state changed\", {\n      user: user ? { name: user.name, isAdmin: user.isAdmin } : null\n    });\n\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      console.log(\"AdminProtectedRoute: Non-admin user detected, redirecting to user hub\");\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    console.log(\"AdminProtectedRoute: User not loaded yet, showing loading...\");\n    return <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n        <p className=\"text-gray-600\">Loading admin panel...</p>\n      </div>\n    </div>;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    console.log(\"AdminProtectedRoute: User is not admin, will redirect\");\n    return null;\n  }\n\n  console.log(\"AdminProtectedRoute: Admin user confirmed, rendering children\");\n  // If user is admin, render the children\n  return children;\n};\n\nexport default AdminProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM;IAAEC;EAAK,CAAC,GAAGR,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdY,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDJ,IAAI,EAAEA,IAAI,GAAG;QAAEK,IAAI,EAAEL,IAAI,CAACK,IAAI;QAAEC,OAAO,EAAEN,IAAI,CAACM;MAAQ,CAAC,GAAG;IAC5D,CAAC,CAAC;;IAEF;IACA,IAAIN,IAAI,IAAI,CAACA,IAAI,CAACM,OAAO,EAAE;MACzBH,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;MACpFV,OAAO,CAACa,KAAK,CAAC,2CAA2C,CAAC;MAC1DL,QAAQ,CAAC,WAAW,CAAC;IACvB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;;EAEpB;EACA,IAAI,CAACF,IAAI,EAAE;IACTG,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3E,oBAAOR,OAAA;MAAKY,SAAS,EAAC,+CAA+C;MAAAV,QAAA,eACnEF,OAAA;QAAKY,SAAS,EAAC,aAAa;QAAAV,QAAA,gBAC1BF,OAAA;UAAKY,SAAS,EAAC;QAA2E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGhB,OAAA;UAAGY,SAAS,EAAC,eAAe;UAAAV,QAAA,EAAC;QAAsB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EACR;;EAEA;EACA,IAAI,CAACZ,IAAI,CAACM,OAAO,EAAE;IACjBH,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,OAAO,IAAI;EACb;EAEAD,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;EAC5E;EACA,OAAON,QAAQ;AACjB,CAAC;AAACC,EAAA,CArCIF,mBAAmB;EAAA,QACNL,WAAW,EACXC,WAAW;AAAA;AAAAoB,EAAA,GAFxBhB,mBAAmB;AAuCzB,eAAeA,mBAAmB;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}