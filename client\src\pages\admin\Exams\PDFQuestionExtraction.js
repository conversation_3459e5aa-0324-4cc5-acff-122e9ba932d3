import React, { useState, useEffect } from 'react';
import { Form, message, Upload, But<PERSON>, Card, Checkbox, Table, Modal, Spin } from 'antd';
import { InboxOutlined, FileTextOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { extractQuestionsFromPDF, approveExtractedQuestions } from '../../../apicalls/pdfExtraction';
import { getAllExams } from '../../../apicalls/exams';

const { Dragger } = Upload;

function PDFQuestionExtraction({ visible, onClose, examId, examData, onQuestionsAdded }) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [step, setStep] = useState(1); // 1: Upload, 2: Configure, 3: Review, 4: Approve
  const [pdfFile, setPdfFile] = useState(null);
  const [extractedQuestions, setExtractedQuestions] = useState([]);
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [extractionMetadata, setExtractionMetadata] = useState(null);
  const [loading, setLoading] = useState(false);

  // Subjects data (same as in AddEditExam)
  const primarySubjects = ["Mathematics", "English", "Kiswahili", "Science & Technology", "Social Studies", "Vocational Skills"];
  const secondarySubjects = ["Mathematics", "English", "Kiswahili", "Biology", "Physics", "Chemistry", "Geography", "History", "Civics", "Commerce", "Accounting", "Book Keeping"];
  const advanceSubjects = ["Mathematics", "English", "Kiswahili", "Biology", "Physics", "Chemistry", "Geography", "History", "Civics", "Commerce", "Accounting", "Book Keeping"];

  const getSubjectsForLevel = (level) => {
    switch (level?.toLowerCase()) {
      case 'primary': return primarySubjects;
      case 'secondary': return secondarySubjects;
      case 'advance': return advanceSubjects;
      default: return primarySubjects;
    }
  };

  const getClassesForLevel = (level) => {
    switch (level?.toLowerCase()) {
      case 'primary': return ['1', '2', '3', '4', '5', '6', '7'];
      case 'secondary': return ['Form-1', 'Form-2', 'Form-3', 'Form-4'];
      case 'advance': return ['Form-5', 'Form-6'];
      default: return ['1', '2', '3', '4', '5', '6', '7'];
    }
  };

  // Initialize form with exam data
  useEffect(() => {
    if (examData) {
      form.setFieldsValue({
        subject: examData.subject || examData.category,
        level: examData.level,
        class: examData.class,
        topic: examData.category
      });
    }
  }, [examData, form]);

  // Handle PDF file upload
  const handleFileUpload = (info) => {
    const { status, originFileObj } = info.file;
    
    if (status === 'done' || originFileObj) {
      setPdfFile(originFileObj || info.file);
      message.success('PDF file uploaded successfully');
      setStep(2);
    } else if (status === 'error') {
      message.error('PDF upload failed');
    }
  };

  // Custom upload request (prevent auto upload)
  const customRequest = ({ file, onSuccess }) => {
    setTimeout(() => {
      onSuccess("ok");
    }, 0);
  };

  // Handle extraction configuration and start extraction
  const handleExtraction = async (values) => {
    if (!pdfFile) {
      message.error('Please upload a PDF file first');
      return;
    }

    try {
      setLoading(true);
      dispatch(ShowLoading());

      // Prepare form data
      const formData = new FormData();
      formData.append('pdf', pdfFile);
      formData.append('examId', examId);
      formData.append('subject', values.subject);
      formData.append('level', values.level);
      formData.append('class', values.class);
      formData.append('topic', values.topic || values.subject);

      console.log('🚀 Starting PDF question extraction...');
      const response = await extractQuestionsFromPDF(formData);

      if (response.success) {
        setExtractedQuestions(response.data.questions);
        setExtractionMetadata(response.data.metadata);
        setSelectedQuestions(response.data.questions.map((_, index) => index)); // Select all by default
        setStep(3);
        message.success(`Successfully extracted ${response.data.questions.length} questions!`);
      } else {
        message.error(response.message || 'Failed to extract questions');
      }
    } catch (error) {
      console.error('Extraction error:', error);
      message.error('An error occurred during extraction');
    } finally {
      setLoading(false);
      dispatch(HideLoading());
    }
  };

  // Handle question selection
  const handleQuestionSelection = (selectedRowKeys) => {
    setSelectedQuestions(selectedRowKeys);
  };

  // Handle question approval and saving
  const handleApproval = async () => {
    if (selectedQuestions.length === 0) {
      message.error('Please select at least one question to approve');
      return;
    }

    try {
      setLoading(true);
      dispatch(ShowLoading());

      const questionsToApprove = selectedQuestions.map(index => extractedQuestions[index]);

      const response = await approveExtractedQuestions({
        examId,
        approvedQuestions: questionsToApprove
      });

      if (response.success) {
        message.success(`Successfully added ${response.data.savedQuestions} questions to the exam!`);
        setStep(4);
        
        // Notify parent component
        if (onQuestionsAdded) {
          onQuestionsAdded(response.data.savedQuestions);
        }
      } else {
        message.error(response.message || 'Failed to approve questions');
      }
    } catch (error) {
      console.error('Approval error:', error);
      message.error('An error occurred during approval');
    } finally {
      setLoading(false);
      dispatch(HideLoading());
    }
  };

  // Reset modal state
  const handleClose = () => {
    setStep(1);
    setPdfFile(null);
    setExtractedQuestions([]);
    setSelectedQuestions([]);
    setExtractionMetadata(null);
    form.resetFields();
    onClose();
  };

  // Table columns for question review
  const questionColumns = [
    {
      title: 'Question',
      dataIndex: 'name',
      key: 'name',
      width: '40%',
      render: (text) => (
        <div className="max-w-md">
          <p className="text-sm font-medium text-gray-900 line-clamp-3">{text}</p>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: '10%',
      render: (type) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          type === 'mcq' ? 'bg-blue-100 text-blue-800' :
          type === 'fill' ? 'bg-green-100 text-green-800' :
          'bg-purple-100 text-purple-800'
        }`}>
          {type.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'Correct Answer',
      dataIndex: 'correctAnswer',
      key: 'correctAnswer',
      width: '20%',
      render: (answer, record) => (
        <div className="text-sm">
          {record.type === 'mcq' && record.options ? (
            <span className="font-medium text-green-600">
              {answer}: {record.options[answer]}
            </span>
          ) : (
            <span className="font-medium text-green-600">{answer}</span>
          )}
        </div>
      ),
    },
    {
      title: 'Confidence',
      dataIndex: 'extractionConfidence',
      key: 'confidence',
      width: '15%',
      render: (confidence) => (
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            confidence >= 0.8 ? 'bg-green-500' :
            confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
          }`}></div>
          <span className="text-sm">{Math.round(confidence * 100)}%</span>
        </div>
      ),
    },
    {
      title: 'Options',
      dataIndex: 'options',
      key: 'options',
      width: '15%',
      render: (options, record) => (
        record.type === 'mcq' && options ? (
          <div className="text-xs space-y-1">
            {Object.entries(options).map(([key, value]) => (
              <div key={key} className="truncate">
                <span className="font-medium">{key}:</span> {value.substring(0, 30)}...
              </div>
            ))}
          </div>
        ) : (
          <span className="text-gray-400 text-xs">N/A</span>
        )
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedQuestions,
    onChange: handleQuestionSelection,
    getCheckboxProps: (record, index) => ({
      name: `question-${index}`,
    }),
  };

  return (
    <Modal
      title="AI PDF Question Extraction"
      open={visible}
      onCancel={handleClose}
      width={1200}
      footer={null}
      destroyOnClose
    >
      <div className="space-y-6">
        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          {[
            { step: 1, title: 'Upload PDF', icon: <InboxOutlined /> },
            { step: 2, title: 'Configure', icon: <FileTextOutlined /> },
            { step: 3, title: 'Review Questions', icon: <ExclamationCircleOutlined /> },
            { step: 4, title: 'Complete', icon: <CheckCircleOutlined /> }
          ].map((item, index) => (
            <div key={item.step} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                step >= item.step 
                  ? 'bg-blue-500 border-blue-500 text-white' 
                  : 'border-gray-300 text-gray-400'
              }`}>
                {item.icon}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                step >= item.step ? 'text-blue-600' : 'text-gray-400'
              }`}>
                {item.title}
              </span>
              {index < 3 && (
                <div className={`w-8 h-0.5 mx-4 ${
                  step > item.step ? 'bg-blue-500' : 'bg-gray-300'
                }`}></div>
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Upload PDF */}
        {step === 1 && (
          <Card title="Upload PDF Document" className="border-2 border-dashed border-gray-300">
            <Dragger
              name="pdf"
              accept=".pdf"
              customRequest={customRequest}
              onChange={handleFileUpload}
              showUploadList={false}
              className="p-8"
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined className="text-6xl text-blue-500" />
              </p>
              <p className="ant-upload-text text-lg font-medium">
                Click or drag PDF file to this area to upload
              </p>
              <p className="ant-upload-hint text-gray-500">
                Upload a PDF document containing questions and answers. 
                The AI will automatically extract and structure the questions.
              </p>
            </Dragger>
          </Card>
        )}

        {/* Step 2: Configure Extraction */}
        {step === 2 && (
          <Card title="Configure Question Extraction">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleExtraction}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Form.Item
                  label="Subject"
                  name="subject"
                  rules={[{ required: true, message: 'Please select a subject' }]}
                >
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">Select Subject</option>
                    {getSubjectsForLevel(examData?.level).map((subject) => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                </Form.Item>

                <Form.Item
                  label="Level"
                  name="level"
                  rules={[{ required: true, message: 'Please select a level' }]}
                >
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">Select Level</option>
                    <option value="Primary">Primary</option>
                    <option value="Secondary">Secondary</option>
                    <option value="Advance">Advance</option>
                  </select>
                </Form.Item>

                <Form.Item
                  label="Class"
                  name="class"
                  rules={[{ required: true, message: 'Please select a class' }]}
                >
                  <select className="w-full p-2 border border-gray-300 rounded-md">
                    <option value="">Select Class</option>
                    {getClassesForLevel(examData?.level).map((cls) => (
                      <option key={cls} value={cls}>
                        {cls}
                      </option>
                    ))}
                  </select>
                </Form.Item>

                <Form.Item
                  label="Topic (Optional)"
                  name="topic"
                >
                  <input 
                    type="text" 
                    placeholder="Enter specific topic"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </Form.Item>
              </div>

              <div className="flex justify-between pt-4">
                <Button onClick={() => setStep(1)}>
                  Back
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  loading={loading}
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  Extract Questions
                </Button>
              </div>
            </Form>
          </Card>
        )}

        {/* Step 3: Review Questions */}
        {step === 3 && (
          <Card title={`Review Extracted Questions (${extractedQuestions.length} found)`}>
            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Extraction Summary:</strong> Found {extractedQuestions.length} questions from your PDF. 
                Review and select the questions you want to add to the exam.
              </p>
              {extractionMetadata && (
                <p className="text-xs text-blue-600 mt-1">
                  Processed {extractionMetadata.textLength} characters of text content.
                </p>
              )}
            </div>

            <Table
              rowSelection={rowSelection}
              columns={questionColumns}
              dataSource={extractedQuestions.map((q, index) => ({ ...q, key: index }))}
              pagination={{ pageSize: 5 }}
              scroll={{ x: 1000 }}
              size="small"
            />

            <div className="flex justify-between pt-4">
              <Button onClick={() => setStep(2)}>
                Back
              </Button>
              <div className="space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedQuestions.length} of {extractedQuestions.length} questions selected
                </span>
                <Button 
                  type="primary" 
                  onClick={handleApproval}
                  disabled={selectedQuestions.length === 0}
                  loading={loading}
                  className="bg-green-500 hover:bg-green-600"
                >
                  Approve & Add Questions
                </Button>
              </div>
            </div>
          </Card>
        )}

        {/* Step 4: Success */}
        {step === 4 && (
          <Card className="text-center">
            <div className="py-8">
              <CheckCircleOutlined className="text-6xl text-green-500 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Questions Added Successfully!
              </h3>
              <p className="text-gray-600 mb-6">
                {selectedQuestions.length} questions have been extracted from your PDF and added to the exam.
              </p>
              <Button 
                type="primary" 
                onClick={handleClose}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Done
              </Button>
            </div>
          </Card>
        )}

        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
            <Spin size="large" />
          </div>
        )}
      </div>
    </Modal>
  );
}

export default PDFQuestionExtraction;
