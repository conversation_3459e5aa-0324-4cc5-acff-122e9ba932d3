const fs = require('fs');
const path = require('path');
const PDFQuestionExtractionService = require('./services/pdfQuestionExtractionService');

async function testPDFExtraction() {
  try {
    console.log('🧪 Testing PDF Question Extraction Service...');
    
    // Create a simple test PDF content (you would normally upload a real PDF)
    const testText = `
    MATHEMATICS TEST - CLASS 5
    
    1. What is 5 + 3?
    A) 6
    B) 7
    C) 8
    D) 9
    
    2. Fill in the blank: 10 - 4 = ____
    
    3. What is the area of a rectangle with length 5 and width 3?
    A) 8
    B) 15
    C) 12
    D) 20
    
    4. Complete: 7 × 3 = ____
    
    Answer Key:
    1. C) 8
    2. 6
    3. B) 15
    4. 21
    `;
    
    // Mock exam configuration
    const examConfig = {
      examId: 'test-exam-id',
      subject: 'Mathematics',
      level: 'Primary',
      class: '5',
      topic: 'Basic Arithmetic'
    };
    
    // Initialize service
    const extractionService = new PDFQuestionExtractionService();
    
    // Test AI extraction directly with text
    console.log('🤖 Testing AI question extraction...');
    const questions = await extractionService.extractQuestionsWithAI(testText, examConfig);
    
    console.log(`✅ Extracted ${questions.length} questions:`);
    questions.forEach((q, index) => {
      console.log(`\n${index + 1}. ${q.name}`);
      console.log(`   Type: ${q.type}`);
      console.log(`   Answer: ${q.correctAnswer}`);
      if (q.options) {
        console.log(`   Options:`, q.options);
      }
      console.log(`   Confidence: ${Math.round(q.extractionConfidence * 100)}%`);
    });
    
    console.log('\n🎉 PDF extraction test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('OpenAI')) {
      console.log('\n💡 Note: Make sure OPENAI_API_KEY is set in your .env file');
    }
  }
}

// Run the test
testPDFExtraction();
