import React, { useState, useEffect, useCallback } from "react";
import { useDispatch } from "react-redux";
import { 
  <PERSON>, 
  Button, 
  Row, 
  Col, 
  Checkbox, 
  message,
  Tag,
  Divider,
  Modal,
  Input,
  Alert
} from "antd";
import {
  FaArrowLeft,
  FaCheck,
  FaTimes,
  FaEye,
  FaEdit,
  FaImage,
  FaQuestionCircle,
  FaTrash
} from "react-icons/fa";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { previewQuestions, approveQuestions, deleteGeneratedQuestion } from "../../../apicalls/aiQuestions";

const { TextArea } = Input;

function QuestionPreview({ generation, onBack, onSuccess }) {
  const dispatch = useDispatch();
  const [previewData, setPreviewData] = useState(null);
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [rejectedQuestions, setRejectedQuestions] = useState([]);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [currentRejectIndex, setCurrentRejectIndex] = useState(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentDeleteIndex, setCurrentDeleteIndex] = useState(null);

  const fetchPreviewData = useCallback(async () => {
    try {
      dispatch(ShowLoading());
      const response = await previewQuestions(generation._id);
      if (response.success) {
        setPreviewData(response.data);
        // Pre-select all questions that aren't already rejected
        const validQuestions = response.data.questions
          .map((_, index) => index)
          .filter(index => !response.data.questions[index].rejectionReason);
        setSelectedQuestions(validQuestions);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error("Failed to fetch preview data");
    } finally {
      dispatch(HideLoading());
    }
  }, [generation, dispatch]);

  useEffect(() => {
    if (generation) {
      fetchPreviewData();
    }
  }, [generation, fetchPreviewData]);

  const handleQuestionSelect = (questionIndex, checked) => {
    if (checked) {
      setSelectedQuestions([...selectedQuestions, questionIndex]);
      // Remove from rejected if it was there
      setRejectedQuestions(rejectedQuestions.filter(r => r.questionIndex !== questionIndex));
    } else {
      setSelectedQuestions(selectedQuestions.filter(index => index !== questionIndex));
    }
  };

  const handleRejectQuestion = (questionIndex) => {
    setCurrentRejectIndex(questionIndex);
    setShowRejectModal(true);
  };

  const confirmRejectQuestion = () => {
    if (!rejectionReason.trim()) {
      message.error("Please provide a rejection reason");
      return;
    }

    const newRejection = {
      questionIndex: currentRejectIndex,
      reason: rejectionReason,
    };

    setRejectedQuestions([...rejectedQuestions, newRejection]);
    setSelectedQuestions(selectedQuestions.filter(index => index !== currentRejectIndex));
    setShowRejectModal(false);
    setRejectionReason("");
    setCurrentRejectIndex(null);
  };

  const handleDeleteQuestion = (questionIndex) => {
    setCurrentDeleteIndex(questionIndex);
    setShowDeleteModal(true);
  };

  const confirmDeleteQuestion = async () => {
    try {
      dispatch(ShowLoading());
      const response = await deleteGeneratedQuestion(generation._id, currentDeleteIndex);

      if (response.success) {
        message.success("Question deleted successfully");

        // Clear selections and rejections that might be affected by index changes
        setSelectedQuestions(prev => prev.filter(index => index !== currentDeleteIndex).map(index =>
          index > currentDeleteIndex ? index - 1 : index
        ));
        setRejectedQuestions(prev => prev.filter(rejection => rejection.questionIndex !== currentDeleteIndex).map(rejection => ({
          ...rejection,
          questionIndex: rejection.questionIndex > currentDeleteIndex ? rejection.questionIndex - 1 : rejection.questionIndex
        })));

        // Refresh the preview data
        await fetchPreviewData();
        setShowDeleteModal(false);
        setCurrentDeleteIndex(null);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error("Failed to delete question");
      console.error("Delete error:", error);
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleApproveQuestions = async () => {
    if (selectedQuestions.length === 0) {
      message.error("Please select at least one question to approve");
      return;
    }

    try {
      dispatch(ShowLoading());
      const payload = {
        generationId: generation._id,
        approvedQuestionIds: selectedQuestions,
        rejectedQuestions: rejectedQuestions,
      };

      const response = await approveQuestions(payload);
      if (response.success) {
        message.success(`${response.data.approvedQuestions} questions approved and added to exam`);
        onSuccess();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error("Failed to approve questions");
    } finally {
      dispatch(HideLoading());
    }
  };

  const getQuestionTypeIcon = (type) => {
    switch (type) {
      case "multiple_choice":
        return <FaQuestionCircle style={{ color: "#1890ff" }} />;
      case "fill_blank":
        return <FaEdit style={{ color: "#52c41a" }} />;
      case "picture_based":
        return <FaImage style={{ color: "#fa8c16" }} />;
      default:
        return <FaQuestionCircle />;
    }
  };

  const getQuestionTypeLabel = (type) => {
    switch (type) {
      case "multiple_choice":
        return "Multiple Choice";
      case "fill_blank":
        return "Fill in the Blank";
      case "picture_based":
        return "Picture-based";
      default:
        return "Unknown";
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "easy":
        return "green";
      case "medium":
        return "orange";
      case "hard":
        return "red";
      default:
        return "default";
    }
  };

  const renderQuestion = (question, index) => {
    const isSelected = selectedQuestions.includes(index);
    const isRejected = rejectedQuestions.some(r => r.questionIndex === index);
    const rejectionInfo = rejectedQuestions.find(r => r.questionIndex === index);

    return (
      <Card
        key={index}
        className={`question-card ${isSelected ? "selected" : ""} ${isRejected ? "rejected" : ""}`}
        title={
          <div className="question-header">
            <div className="question-meta">
              <span className="question-number">Question {index + 1}</span>
              {getQuestionTypeIcon(question.questionType)}
              <Tag color={getDifficultyColor(question.difficultyLevel)}>
                {question.difficultyLevel?.toUpperCase()}
              </Tag>
              <Tag>{getQuestionTypeLabel(question.questionType)}</Tag>
            </div>
            <div className="question-actions">
              <Checkbox
                checked={isSelected}
                onChange={(e) => handleQuestionSelect(index, e.target.checked)}
                disabled={isRejected}
              >
                Approve
              </Checkbox>
              <Button
                type="text"
                danger
                icon={<FaTimes />}
                onClick={() => handleRejectQuestion(index)}
                disabled={isRejected}
              >
                Reject
              </Button>
              <Button
                type="text"
                danger
                icon={<FaTrash />}
                onClick={() => handleDeleteQuestion(index)}
                title="Delete question permanently"
              >
                Delete
              </Button>
            </div>
          </div>
        }
      >
        <div className="question-content">
          <h4>{question.name}</h4>
          
          {question.answerType === "Options" && question.options && (
            <div className="options">
              {Object.entries(question.options).map(([key, value]) => (
                <div 
                  key={key} 
                  className={`option ${key === question.correctOption ? "correct" : ""}`}
                >
                  <strong>{key}:</strong> {value}
                </div>
              ))}
              <div className="correct-answer">
                <strong>Correct Answer:</strong> {question.correctOption}
              </div>
            </div>
          )}

          {question.answerType === "Fill in the Blank" && (
            <div className="fill-blank-answer">
              <strong>Correct Answer:</strong> {question.correctAnswer}
            </div>
          )}

          {question.imageDescription && (
            <div className="image-description">
              <FaImage /> <strong>Image Description:</strong> {question.imageDescription}
            </div>
          )}

          {question.syllabusTopics && question.syllabusTopics.length > 0 && (
            <div className="syllabus-topics">
              <strong>Topics:</strong>
              {question.syllabusTopics.map((topic, i) => (
                <Tag key={i} color="blue">{topic}</Tag>
              ))}
            </div>
          )}

          {isRejected && rejectionInfo && (
            <Alert
              message="Question Rejected"
              description={rejectionInfo.reason}
              type="error"
              showIcon
              className="mt-2"
            />
          )}
        </div>
      </Card>
    );
  };

  if (!previewData) {
    return <div>Loading preview...</div>;
  }

  return (
    <div className="question-preview">
      <Card
        title={
          <div className="preview-header">
            <Button
              type="text"
              icon={<FaArrowLeft />}
              onClick={onBack}
              className="back-button"
            >
              Back to Dashboard
            </Button>
            <div className="title-section">
              <FaEye className="title-icon" />
              <span>Preview Generated Questions</span>
            </div>
          </div>
        }
        extra={
          <div className="preview-actions">
            <span className="selection-info">
              {selectedQuestions.length} of {previewData.questions.length} selected
            </span>
            <Button
              type="primary"
              icon={<FaCheck />}
              onClick={handleApproveQuestions}
              disabled={selectedQuestions.length === 0}
            >
              Approve Selected Questions
            </Button>
          </div>
        }
      >
        <div className="preview-summary">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <div className="summary-item">
                <strong>Total Questions:</strong> {previewData.totalQuestions}
              </div>
            </Col>
            <Col span={6}>
              <div className="summary-item">
                <strong>Status:</strong> 
                <Tag color="green">{previewData.status.toUpperCase()}</Tag>
              </div>
            </Col>
            <Col span={6}>
              <div className="summary-item">
                <strong>Generation Time:</strong> {previewData.generationTime}ms
              </div>
            </Col>
            <Col span={6}>
              <div className="summary-item">
                <strong>Selected:</strong> {selectedQuestions.length}
              </div>
            </Col>
          </Row>
        </div>

        <Divider />

        <div className="questions-list">
          {previewData.questions.map((question, index) => renderQuestion(question, index))}
        </div>
      </Card>

      <Modal
        title="Reject Question"
        open={showRejectModal}
        onOk={confirmRejectQuestion}
        onCancel={() => {
          setShowRejectModal(false);
          setRejectionReason("");
          setCurrentRejectIndex(null);
        }}
        okText="Reject"
        okButtonProps={{ danger: true }}
      >
        <p>Please provide a reason for rejecting this question:</p>
        <TextArea
          value={rejectionReason}
          onChange={(e) => setRejectionReason(e.target.value)}
          placeholder="Enter rejection reason..."
          rows={4}
        />
      </Modal>

      <Modal
        title="Delete Question"
        open={showDeleteModal}
        onOk={confirmDeleteQuestion}
        onCancel={() => {
          setShowDeleteModal(false);
          setCurrentDeleteIndex(null);
        }}
        okText="Delete"
        okButtonProps={{ danger: true }}
      >
        <p>Are you sure you want to permanently delete this question?</p>
        <p><strong>This action cannot be undone.</strong></p>
        {currentDeleteIndex !== null && previewData && (
          <div className="mt-3 p-3 bg-gray-50 rounded">
            <strong>Question {currentDeleteIndex + 1}:</strong>
            <p className="mt-1">{previewData.questions[currentDeleteIndex]?.name}</p>
          </div>
        )}
      </Modal>
    </div>
  );
}

export default QuestionPreview;
