const express = require("express");
const mongoose = require("mongoose");
require("dotenv").config();

console.log("🚀 Starting minimal test server...");

const app = express();
const port = process.env.PORT || 5000;

// Basic middleware
app.use(express.json());

// Test route
app.get("/test", (req, res) => {
  res.json({
    message: "Server is working!",
    timestamp: new Date().toISOString(),
    database: mongoose.connection.readyState === 1 ? "Connected" : "Disconnected"
  });
});

// Database connection
async function connectDB() {
  try {
    console.log("📡 Connecting to database...");
    await mongoose.connect(process.env.MONGO_URL, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4
    });
    console.log("✅ Database connected successfully!");
  } catch (error) {
    console.error("❌ Database connection failed:", error.message);
  }
}

// Start server
app.listen(port, async () => {
  console.log(`✅ Server running on port ${port}`);
  console.log(`🌐 Test URL: http://localhost:${port}/test`);

  // Connect to database after server starts
  await connectDB();

  console.log("🎉 Server and database ready!");
});

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await mongoose.connection.close();
  console.log('✅ Database connection closed');
  process.exit(0);
});
