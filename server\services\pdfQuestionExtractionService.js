const pdf = require('pdf-parse');
const fs = require('fs');
const path = require('path');
const OpenAI = require('openai');

class PDFQuestionExtractionService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  // Extract text from PDF
  async extractTextFromPDF(pdfBuffer) {
    try {
      console.log('📄 Extracting text from PDF...');
      const data = await pdf(pdfBuffer);
      console.log(`✅ Extracted ${data.text.length} characters from PDF`);
      return data.text;
    } catch (error) {
      console.error('❌ Error extracting text from PDF:', error);
      throw new Error('Failed to extract text from PDF: ' + error.message);
    }
  }

  // Use AI to extract and structure questions from text
  async extractQuestionsWithAI(text, examConfig) {
    try {
      console.log('🤖 Using AI to extract questions from text...');
      
      const prompt = this.buildExtractionPrompt(text, examConfig);
      
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an expert educational content analyzer. Extract questions from the provided text and format them according to the specified structure."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 4000,
      });

      const aiResponse = response.choices[0].message.content;
      console.log('🤖 AI Response received, parsing questions...');
      
      return this.parseAIResponse(aiResponse, examConfig);
    } catch (error) {
      console.error('❌ Error in AI question extraction:', error);
      throw new Error('Failed to extract questions with AI: ' + error.message);
    }
  }

  // Build the AI prompt for question extraction
  buildExtractionPrompt(text, examConfig) {
    return `
Extract questions from the following educational content and format them according to these specifications:

EXAM CONFIGURATION:
- Subject: ${examConfig.subject}
- Level: ${examConfig.level}
- Class: ${examConfig.class}
- Topic: ${examConfig.topic || 'General'}

CONTENT TO ANALYZE:
${text.substring(0, 8000)} ${text.length > 8000 ? '...(truncated)' : ''}

INSTRUCTIONS:
1. Identify all questions in the content (multiple choice, fill-in-the-blank, short answer)
2. For each question, determine the correct answer
3. Format the output as a JSON array with this exact structure:

[
  {
    "name": "Question text here",
    "type": "mcq|fill|text",
    "options": {
      "A": "Option A text",
      "B": "Option B text", 
      "C": "Option C text",
      "D": "Option D text"
    },
    "correctAnswer": "A|B|C|D or exact answer text",
    "topic": "${examConfig.topic || 'General'}",
    "classLevel": "${examConfig.class}",
    "difficultyLevel": "easy|medium|hard",
    "extractionConfidence": 0.0-1.0
  }
]

QUESTION TYPE RULES:
- "mcq": Multiple choice questions with 4 options (A, B, C, D)
- "fill": Fill-in-the-blank questions (omit options field)
- "text": Short answer questions (omit options field)

IMPORTANT:
- Only include questions that have clear, identifiable answers
- For MCQ questions, always provide exactly 4 options (A, B, C, D)
- For fill/text questions, do not include the options field
- Ensure correctAnswer matches exactly with one of the options for MCQ
- Set extractionConfidence based on how certain you are about the question and answer
- Maintain the original question wording as much as possible
- If no clear questions are found, return an empty array []

Return only the JSON array, no additional text or explanation.
`;
  }

  // Parse AI response and validate question structure
  parseAIResponse(aiResponse, examConfig) {
    try {
      // Clean the response to extract JSON
      let jsonStr = aiResponse.trim();
      
      // Remove any markdown formatting
      if (jsonStr.startsWith('```json')) {
        jsonStr = jsonStr.replace(/```json\n?/, '').replace(/\n?```$/, '');
      } else if (jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/```\n?/, '').replace(/\n?```$/, '');
      }

      const questions = JSON.parse(jsonStr);
      
      if (!Array.isArray(questions)) {
        throw new Error('AI response is not an array');
      }

      console.log(`✅ Parsed ${questions.length} questions from AI response`);
      
      // Validate and enhance each question
      const validatedQuestions = questions.map((q, index) => {
        return this.validateAndEnhanceQuestion(q, examConfig, index);
      }).filter(q => q !== null);

      console.log(`✅ Validated ${validatedQuestions.length} questions`);
      return validatedQuestions;
      
    } catch (error) {
      console.error('❌ Error parsing AI response:', error);
      console.log('Raw AI response:', aiResponse);
      throw new Error('Failed to parse AI response: ' + error.message);
    }
  }

  // Validate and enhance individual question
  validateAndEnhanceQuestion(question, examConfig, index) {
    try {
      // Required fields validation
      if (!question.name || !question.type || !question.correctAnswer) {
        console.warn(`⚠️ Question ${index + 1} missing required fields, skipping`);
        return null;
      }

      // Validate question type
      if (!['mcq', 'fill', 'text'].includes(question.type)) {
        console.warn(`⚠️ Question ${index + 1} has invalid type: ${question.type}, defaulting to mcq`);
        question.type = 'mcq';
      }

      // Validate MCQ questions
      if (question.type === 'mcq') {
        if (!question.options || typeof question.options !== 'object') {
          console.warn(`⚠️ MCQ Question ${index + 1} missing options, skipping`);
          return null;
        }

        const optionKeys = Object.keys(question.options);
        if (optionKeys.length < 2) {
          console.warn(`⚠️ MCQ Question ${index + 1} has insufficient options, skipping`);
          return null;
        }

        // Ensure correct answer is valid for MCQ
        if (!optionKeys.includes(question.correctAnswer)) {
          console.warn(`⚠️ MCQ Question ${index + 1} correct answer not in options, skipping`);
          return null;
        }
      }

      // Remove options for non-MCQ questions
      if (question.type !== 'mcq' && question.options) {
        delete question.options;
      }

      // Set default values
      const enhancedQuestion = {
        name: question.name.trim(),
        type: question.type,
        correctAnswer: question.correctAnswer,
        topic: question.topic || examConfig.topic || 'General',
        classLevel: question.classLevel || examConfig.class,
        difficultyLevel: question.difficultyLevel || 'medium',
        duration: 90, // Default 90 seconds per question
        createdBy: 'ai',
        isAIGenerated: true,
        generationSource: 'pdf_extraction',
        extractionConfidence: question.extractionConfidence || 0.8,
        // Add metadata about extraction
        extractionMetadata: {
          sourceType: 'pdf',
          extractedAt: new Date(),
          examConfig: {
            subject: examConfig.subject,
            level: examConfig.level,
            class: examConfig.class
          }
        }
      };

      // Add options for MCQ questions
      if (question.type === 'mcq' && question.options) {
        enhancedQuestion.options = question.options;
      }

      return enhancedQuestion;
      
    } catch (error) {
      console.error(`❌ Error validating question ${index + 1}:`, error);
      return null;
    }
  }

  // Main extraction method
  async extractQuestionsFromPDF(pdfBuffer, examConfig) {
    try {
      console.log('🚀 Starting PDF question extraction process...');
      
      // Step 1: Extract text from PDF
      const text = await this.extractTextFromPDF(pdfBuffer);
      
      if (!text || text.trim().length < 100) {
        throw new Error('PDF contains insufficient text content for question extraction');
      }

      // Step 2: Use AI to extract questions
      const questions = await this.extractQuestionsWithAI(text, examConfig);
      
      if (questions.length === 0) {
        throw new Error('No valid questions could be extracted from the PDF');
      }

      console.log(`🎉 Successfully extracted ${questions.length} questions from PDF`);
      
      return {
        success: true,
        questions: questions,
        metadata: {
          totalQuestions: questions.length,
          textLength: text.length,
          extractedAt: new Date(),
          examConfig: examConfig
        }
      };
      
    } catch (error) {
      console.error('❌ PDF question extraction failed:', error);
      throw error;
    }
  }
}

module.exports = PDFQuestionExtractionService;
