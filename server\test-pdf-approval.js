require('dotenv').config();
const mongoose = require('mongoose');
const Question = require('./models/questionModel');
const Exam = require('./models/examModel');

async function testPDFApproval() {
  try {
    console.log('🧪 Testing PDF approval process...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find an existing exam
    const exam = await Exam.findOne();
    if (!exam) {
      console.log('❌ No exams found in database');
      return;
    }
    
    console.log(`📋 Using exam: "${exam.name}" (ID: ${exam._id})`);
    console.log(`📊 Current questions count: ${exam.questions.length}`);
    
    // Simulate the PDF extraction approval process
    const approvedQuestions = [
      {
        name: "What is 5 + 3?",
        type: "mcq",
        options: {
          A: "6",
          B: "7", 
          C: "8",
          D: "9"
        },
        correctAnswer: "C",
        topic: "Mathematics",
        classLevel: exam.class || "5",
        difficultyLevel: "easy",
        duration: 90,
        createdBy: "ai",
        isAIGenerated: true,
        generationSource: "pdf_extraction",
        extractionConfidence: 0.95
      },
      {
        name: "Fill in the blank: 10 - 4 = ____",
        type: "fill",
        correctAnswer: "6",
        topic: "Mathematics", 
        classLevel: exam.class || "5",
        difficultyLevel: "easy",
        duration: 90,
        createdBy: "ai",
        isAIGenerated: true,
        generationSource: "pdf_extraction",
        extractionConfidence: 0.90
      }
    ];
    
    console.log(`📝 Simulating approval of ${approvedQuestions.length} questions...`);
    
    const savedQuestions = [];
    const errors = [];
    
    // Process each approved question (same logic as the route)
    for (let i = 0; i < approvedQuestions.length; i++) {
      try {
        const questionData = approvedQuestions[i];
        
        console.log(`📝 Processing question ${i + 1}:`, {
          name: questionData.name?.substring(0, 50) + '...',
          type: questionData.type,
          hasOptions: !!questionData.options,
          correctAnswer: questionData.correctAnswer
        });
        
        // Ensure exam reference is set
        questionData.exam = exam._id;
        
        // Create and save question
        const newQuestion = new Question(questionData);
        console.log(`💾 Saving question ${i + 1} to database...`);
        const savedQuestion = await newQuestion.save();
        console.log(`✅ Question ${i + 1} saved with ID: ${savedQuestion._id}`);
        
        // Add question to exam
        exam.questions.push(savedQuestion._id);
        console.log(`🔗 Added question ${savedQuestion._id} to exam questions array`);
        
        savedQuestions.push(savedQuestion);
        console.log(`✅ Question ${i + 1} processed successfully`);
        
      } catch (error) {
        console.error(`❌ Error saving question ${i + 1}:`, error.message);
        errors.push({
          questionIndex: i,
          error: error.message
        });
      }
    }
    
    // Save exam with new questions
    console.log('💾 Saving exam with new questions...');
    await exam.save();
    
    console.log(`🎉 Successfully saved ${savedQuestions.length} questions to exam`);
    console.log(`📊 Exam now has ${exam.questions.length} total questions`);
    
    // Verify questions were saved by re-fetching the exam
    const updatedExam = await Exam.findById(exam._id).populate("questions");
    console.log(`✅ Verification: Exam has ${updatedExam.questions.length} questions after save`);
    
    if (updatedExam.questions.length > 0) {
      console.log('\n📝 Questions in exam:');
      updatedExam.questions.forEach((q, index) => {
        console.log(`${index + 1}. ${q.name} (${q.type}) - ${q.correctAnswer}`);
      });
    }
    
    // Clean up test questions
    console.log('\n🧹 Cleaning up test questions...');
    for (const question of savedQuestions) {
      await Question.findByIdAndDelete(question._id);
      exam.questions.pull(question._id);
    }
    await exam.save();
    console.log('✅ Test questions cleaned up');
    
    console.log('\n🎉 PDF approval test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

testPDFApproval();
