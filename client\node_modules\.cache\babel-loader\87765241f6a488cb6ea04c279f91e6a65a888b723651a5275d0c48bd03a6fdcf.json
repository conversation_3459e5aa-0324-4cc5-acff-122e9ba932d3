{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Exams\\\\PDFQuestionExtraction.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Form, message, Upload, Button, Card, Checkbox, Table, Modal, Spin } from 'antd';\nimport { InboxOutlined, FileTextOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { useDispatch } from 'react-redux';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { extractQuestionsFromPDF, approveExtractedQuestions } from '../../../apicalls/pdfExtraction';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  <PERSON><PERSON>\n} = Upload;\nfunction PDFQuestionExtraction({\n  visible,\n  onClose,\n  examId,\n  examData,\n  onQuestionsAdded\n}) {\n  _s();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [step, setStep] = useState(1); // 1: Upload, 2: Configure, 3: Review, 4: Approve\n  const [pdfFile, setPdfFile] = useState(null);\n  const [extractedQuestions, setExtractedQuestions] = useState([]);\n  const [selectedQuestions, setSelectedQuestions] = useState([]);\n  const [extractionMetadata, setExtractionMetadata] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // Subjects data (same as in AddEditExam)\n  const primarySubjects = [\"Mathematics\", \"English\", \"Kiswahili\", \"Science & Technology\", \"Social Studies\", \"Vocational Skills\"];\n  const secondarySubjects = [\"Mathematics\", \"English\", \"Kiswahili\", \"Biology\", \"Physics\", \"Chemistry\", \"Geography\", \"History\", \"Civics\", \"Commerce\", \"Accounting\", \"Book Keeping\"];\n  const advanceSubjects = [\"Mathematics\", \"English\", \"Kiswahili\", \"Biology\", \"Physics\", \"Chemistry\", \"Geography\", \"History\", \"Civics\", \"Commerce\", \"Accounting\", \"Book Keeping\"];\n  const getSubjectsForLevel = level => {\n    switch (level === null || level === void 0 ? void 0 : level.toLowerCase()) {\n      case 'primary':\n        return primarySubjects;\n      case 'secondary':\n        return secondarySubjects;\n      case 'advance':\n        return advanceSubjects;\n      default:\n        return primarySubjects;\n    }\n  };\n  const getClassesForLevel = level => {\n    switch (level === null || level === void 0 ? void 0 : level.toLowerCase()) {\n      case 'primary':\n        return ['1', '2', '3', '4', '5', '6', '7'];\n      case 'secondary':\n        return ['Form-1', 'Form-2', 'Form-3', 'Form-4'];\n      case 'advance':\n        return ['Form-5', 'Form-6'];\n      default:\n        return ['1', '2', '3', '4', '5', '6', '7'];\n    }\n  };\n\n  // Initialize form with exam data\n  useEffect(() => {\n    if (examData) {\n      form.setFieldsValue({\n        subject: examData.subject || examData.category,\n        level: examData.level,\n        class: examData.class,\n        topic: examData.category\n      });\n    }\n  }, [examData, form]);\n\n  // Handle PDF file upload\n  const handleFileUpload = info => {\n    const {\n      status,\n      originFileObj\n    } = info.file;\n    if (status === 'done' || originFileObj) {\n      setPdfFile(originFileObj || info.file);\n      message.success('PDF file uploaded successfully');\n      setStep(2);\n    } else if (status === 'error') {\n      message.error('PDF upload failed');\n    }\n  };\n\n  // Custom upload request (prevent auto upload)\n  const customRequest = ({\n    file,\n    onSuccess\n  }) => {\n    setTimeout(() => {\n      onSuccess(\"ok\");\n    }, 0);\n  };\n\n  // Handle extraction configuration and start extraction\n  const handleExtraction = async values => {\n    if (!pdfFile) {\n      message.error('Please upload a PDF file first');\n      return;\n    }\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n\n      // Prepare form data\n      const formData = new FormData();\n      formData.append('pdf', pdfFile);\n      formData.append('examId', examId);\n      formData.append('subject', values.subject);\n      formData.append('level', values.level);\n      formData.append('class', values.class);\n      formData.append('topic', values.topic || values.subject);\n      console.log('🚀 Starting PDF question extraction...');\n      const response = await extractQuestionsFromPDF(formData);\n      if (response.success) {\n        setExtractedQuestions(response.data.questions);\n        setExtractionMetadata(response.data.metadata);\n        setSelectedQuestions(response.data.questions.map((_, index) => index)); // Select all by default\n        setStep(3);\n        message.success(`Successfully extracted ${response.data.questions.length} questions!`);\n      } else {\n        message.error(response.message || 'Failed to extract questions');\n      }\n    } catch (error) {\n      console.error('Extraction error:', error);\n      message.error('An error occurred during extraction');\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  // Handle question selection\n  const handleQuestionSelection = selectedRowKeys => {\n    setSelectedQuestions(selectedRowKeys);\n  };\n\n  // Handle question approval and saving\n  const handleApproval = async () => {\n    if (selectedQuestions.length === 0) {\n      message.error('Please select at least one question to approve');\n      return;\n    }\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      const questionsToApprove = selectedQuestions.map(index => extractedQuestions[index]);\n      console.log('🚀 Approving questions:', {\n        examId,\n        selectedCount: selectedQuestions.length,\n        totalExtracted: extractedQuestions.length,\n        questionsToApprove: questionsToApprove.map(q => {\n          var _q$name;\n          return {\n            name: ((_q$name = q.name) === null || _q$name === void 0 ? void 0 : _q$name.substring(0, 50)) + '...',\n            type: q.type,\n            hasOptions: !!q.options,\n            correctAnswer: q.correctAnswer\n          };\n        })\n      });\n      const response = await approveExtractedQuestions({\n        examId,\n        approvedQuestions: questionsToApprove\n      });\n      console.log('📥 Approval response:', response);\n      if (response.success) {\n        message.success(`Successfully added ${response.data.savedQuestions} questions to the exam!`);\n        setStep(4);\n\n        // Notify parent component\n        if (onQuestionsAdded) {\n          onQuestionsAdded(response.data.savedQuestions);\n        }\n      } else {\n        message.error(response.message || 'Failed to approve questions');\n      }\n    } catch (error) {\n      console.error('Approval error:', error);\n      message.error('An error occurred during approval');\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  // Reset modal state\n  const handleClose = () => {\n    setStep(1);\n    setPdfFile(null);\n    setExtractedQuestions([]);\n    setSelectedQuestions([]);\n    setExtractionMetadata(null);\n    form.resetFields();\n    onClose();\n  };\n\n  // Table columns for question review\n  const questionColumns = [{\n    title: 'Question',\n    dataIndex: 'name',\n    key: 'name',\n    width: '40%',\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm font-medium text-gray-900 line-clamp-3\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Type',\n    dataIndex: 'type',\n    key: 'type',\n    width: '10%',\n    render: type => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `px-2 py-1 rounded-full text-xs font-medium ${type === 'mcq' ? 'bg-blue-100 text-blue-800' : type === 'fill' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800'}`,\n      children: type.toUpperCase()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Correct Answer',\n    dataIndex: 'correctAnswer',\n    key: 'correctAnswer',\n    width: '20%',\n    render: (answer, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-sm\",\n      children: record.type === 'mcq' && record.options ? /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-green-600\",\n        children: [answer, \": \", record.options[answer]]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium text-green-600\",\n        children: answer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Confidence',\n    dataIndex: 'extractionConfidence',\n    key: 'confidence',\n    width: '15%',\n    render: confidence => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-2 h-2 rounded-full mr-2 ${confidence >= 0.8 ? 'bg-green-500' : confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm\",\n        children: [Math.round(confidence * 100), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Options',\n    dataIndex: 'options',\n    key: 'options',\n    width: '15%',\n    render: (options, record) => record.type === 'mcq' && options ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs space-y-1\",\n      children: Object.entries(options).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"truncate\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium\",\n          children: [key, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 17\n        }, this), \" \", value.substring(0, 30), \"...\"]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 15\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-gray-400 text-xs\",\n      children: \"N/A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 11\n    }, this)\n  }];\n  const rowSelection = {\n    selectedRowKeys: selectedQuestions,\n    onChange: handleQuestionSelection,\n    getCheckboxProps: (record, index) => ({\n      name: `question-${index}`\n    })\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: \"AI PDF Question Extraction\",\n    open: visible,\n    onCancel: handleClose,\n    width: 1200,\n    footer: null,\n    destroyOnClose: true,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center space-x-4 mb-8\",\n        children: [{\n          step: 1,\n          title: 'Upload PDF',\n          icon: /*#__PURE__*/_jsxDEV(InboxOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 51\n          }, this)\n        }, {\n          step: 2,\n          title: 'Configure',\n          icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 50\n          }, this)\n        }, {\n          step: 3,\n          title: 'Review Questions',\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 57\n          }, this)\n        }, {\n          step: 4,\n          title: 'Complete',\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 49\n          }, this)\n        }].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center justify-center w-10 h-10 rounded-full border-2 ${step >= item.step ? 'bg-blue-500 border-blue-500 text-white' : 'border-gray-300 text-gray-400'}`,\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `ml-2 text-sm font-medium ${step >= item.step ? 'text-blue-600' : 'text-gray-400'}`,\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), index < 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-8 h-0.5 mx-4 ${step > item.step ? 'bg-blue-500' : 'bg-gray-300'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this)]\n        }, item.step, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), step === 1 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"Upload PDF Document\",\n        className: \"border-2 border-dashed border-gray-300\",\n        children: /*#__PURE__*/_jsxDEV(Dragger, {\n          name: \"pdf\",\n          accept: \".pdf\",\n          customRequest: customRequest,\n          onChange: handleFileUpload,\n          showUploadList: false,\n          className: \"p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"ant-upload-drag-icon\",\n            children: /*#__PURE__*/_jsxDEV(InboxOutlined, {\n              className: \"text-6xl text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"ant-upload-text text-lg font-medium\",\n            children: \"Click or drag PDF file to this area to upload\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"ant-upload-hint text-gray-500\",\n            children: \"Upload a PDF document containing questions and answers. The AI will automatically extract and structure the questions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"Configure Question Extraction\",\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          onFinish: handleExtraction,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Subject\",\n              name: \"subject\",\n              rules: [{\n                required: true,\n                message: 'Please select a subject'\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full p-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Subject\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), getSubjectsForLevel(examData === null || examData === void 0 ? void 0 : examData.level).map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subject,\n                  children: subject\n                }, subject, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Level\",\n              name: \"level\",\n              rules: [{\n                required: true,\n                message: 'Please select a level'\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full p-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Primary\",\n                  children: \"Primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Secondary\",\n                  children: \"Secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Advance\",\n                  children: \"Advance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Class\",\n              name: \"class\",\n              rules: [{\n                required: true,\n                message: 'Please select a class'\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full p-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Class\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this), getClassesForLevel(examData === null || examData === void 0 ? void 0 : examData.level).map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: cls,\n                  children: cls\n                }, cls, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Topic (Optional)\",\n              name: \"topic\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Enter specific topic\",\n                className: \"w-full p-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between pt-4\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setStep(1),\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              className: \"bg-blue-500 hover:bg-blue-600\",\n              children: \"Extract Questions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(Card, {\n        title: `Review Extracted Questions (${extractedQuestions.length} found)`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-4 bg-blue-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-800\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Extraction Summary:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), \" Found \", extractedQuestions.length, \" questions from your PDF. Review and select the questions you want to add to the exam.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), extractionMetadata && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-blue-600 mt-1\",\n            children: [\"Processed \", extractionMetadata.textLength, \" characters of text content.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Table, {\n          rowSelection: rowSelection,\n          columns: questionColumns,\n          dataSource: extractedQuestions.map((q, index) => ({\n            ...q,\n            key: index\n          })),\n          pagination: {\n            pageSize: 5\n          },\n          scroll: {\n            x: 1000\n          },\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setStep(2),\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [selectedQuestions.length, \" of \", extractedQuestions.length, \" questions selected\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: handleApproval,\n              disabled: selectedQuestions.length === 0,\n              loading: loading,\n              className: \"bg-green-500 hover:bg-green-600\",\n              children: \"Approve & Add Questions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this), step === 4 && /*#__PURE__*/_jsxDEV(Card, {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-8\",\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n            className: \"text-6xl text-green-500 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"Questions Added Successfully!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: [selectedQuestions.length, \" questions have been extracted from your PDF and added to the exam.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleClose,\n            className: \"bg-blue-500 hover:bg-blue-600\",\n            children: \"Done\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n}\n_s(PDFQuestionExtraction, \"zacztDANWwzAQqPgk61TL83qOIQ=\", false, function () {\n  return [useDispatch, Form.useForm];\n});\n_c = PDFQuestionExtraction;\nexport default PDFQuestionExtraction;\nvar _c;\n$RefreshReg$(_c, \"PDFQuestionExtraction\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Form", "message", "Upload", "<PERSON><PERSON>", "Card", "Checkbox", "Table", "Modal", "Spin", "InboxOutlined", "FileTextOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "useDispatch", "HideLoading", "ShowLoading", "extractQuestionsFromPDF", "approveExtractedQuestions", "getAllExams", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "PDFQuestionExtraction", "visible", "onClose", "examId", "examData", "onQuestionsAdded", "_s", "dispatch", "form", "useForm", "step", "setStep", "pdfFile", "setPdfFile", "extractedQuestions", "setExtractedQuestions", "selectedQuestions", "setSelectedQuestions", "extractionMetadata", "setExtractionMetadata", "loading", "setLoading", "primarySubjects", "secondarySubjects", "advanceSubjects", "getSubjectsForLevel", "level", "toLowerCase", "getClassesForLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subject", "category", "class", "topic", "handleFileUpload", "info", "status", "originFileObj", "file", "success", "error", "customRequest", "onSuccess", "setTimeout", "handleExtraction", "values", "formData", "FormData", "append", "console", "log", "response", "data", "questions", "metadata", "map", "_", "index", "length", "handleQuestionSelection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleApproval", "questionsToApprove", "selectedCount", "totalExtracted", "q", "_q$name", "name", "substring", "type", "hasOptions", "options", "<PERSON><PERSON><PERSON><PERSON>", "approvedQuestions", "savedQuestions", "handleClose", "resetFields", "questionColumns", "title", "dataIndex", "key", "width", "render", "text", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toUpperCase", "answer", "record", "confidence", "Math", "round", "Object", "entries", "value", "rowSelection", "onChange", "getCheckboxProps", "open", "onCancel", "footer", "destroyOnClose", "icon", "item", "accept", "showUploadList", "layout", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "cls", "placeholder", "onClick", "htmlType", "textLength", "columns", "dataSource", "pagination", "pageSize", "scroll", "x", "size", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Exams/PDFQuestionExtraction.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Form, message, Upload, But<PERSON>, Card, Checkbox, Table, Modal, Spin } from 'antd';\nimport { InboxOutlined, FileTextOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { useDispatch } from 'react-redux';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { extractQuestionsFromPDF, approveExtractedQuestions } from '../../../apicalls/pdfExtraction';\nimport { getAllExams } from '../../../apicalls/exams';\n\nconst { Dragger } = Upload;\n\nfunction PDFQuestionExtraction({ visible, onClose, examId, examData, onQuestionsAdded }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [step, setStep] = useState(1); // 1: Upload, 2: Configure, 3: Review, 4: Approve\n  const [pdfFile, setPdfFile] = useState(null);\n  const [extractedQuestions, setExtractedQuestions] = useState([]);\n  const [selectedQuestions, setSelectedQuestions] = useState([]);\n  const [extractionMetadata, setExtractionMetadata] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  // Subjects data (same as in AddEditExam)\n  const primarySubjects = [\"Mathematics\", \"English\", \"Kiswahili\", \"Science & Technology\", \"Social Studies\", \"Vocational Skills\"];\n  const secondarySubjects = [\"Mathematics\", \"English\", \"Kiswahili\", \"Biology\", \"Physics\", \"Chemistry\", \"Geography\", \"History\", \"Civics\", \"Commerce\", \"Accounting\", \"Book Keeping\"];\n  const advanceSubjects = [\"Mathematics\", \"English\", \"Kiswahili\", \"Biology\", \"Physics\", \"Chemistry\", \"Geography\", \"History\", \"Civics\", \"Commerce\", \"Accounting\", \"Book Keeping\"];\n\n  const getSubjectsForLevel = (level) => {\n    switch (level?.toLowerCase()) {\n      case 'primary': return primarySubjects;\n      case 'secondary': return secondarySubjects;\n      case 'advance': return advanceSubjects;\n      default: return primarySubjects;\n    }\n  };\n\n  const getClassesForLevel = (level) => {\n    switch (level?.toLowerCase()) {\n      case 'primary': return ['1', '2', '3', '4', '5', '6', '7'];\n      case 'secondary': return ['Form-1', 'Form-2', 'Form-3', 'Form-4'];\n      case 'advance': return ['Form-5', 'Form-6'];\n      default: return ['1', '2', '3', '4', '5', '6', '7'];\n    }\n  };\n\n  // Initialize form with exam data\n  useEffect(() => {\n    if (examData) {\n      form.setFieldsValue({\n        subject: examData.subject || examData.category,\n        level: examData.level,\n        class: examData.class,\n        topic: examData.category\n      });\n    }\n  }, [examData, form]);\n\n  // Handle PDF file upload\n  const handleFileUpload = (info) => {\n    const { status, originFileObj } = info.file;\n    \n    if (status === 'done' || originFileObj) {\n      setPdfFile(originFileObj || info.file);\n      message.success('PDF file uploaded successfully');\n      setStep(2);\n    } else if (status === 'error') {\n      message.error('PDF upload failed');\n    }\n  };\n\n  // Custom upload request (prevent auto upload)\n  const customRequest = ({ file, onSuccess }) => {\n    setTimeout(() => {\n      onSuccess(\"ok\");\n    }, 0);\n  };\n\n  // Handle extraction configuration and start extraction\n  const handleExtraction = async (values) => {\n    if (!pdfFile) {\n      message.error('Please upload a PDF file first');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n\n      // Prepare form data\n      const formData = new FormData();\n      formData.append('pdf', pdfFile);\n      formData.append('examId', examId);\n      formData.append('subject', values.subject);\n      formData.append('level', values.level);\n      formData.append('class', values.class);\n      formData.append('topic', values.topic || values.subject);\n\n      console.log('🚀 Starting PDF question extraction...');\n      const response = await extractQuestionsFromPDF(formData);\n\n      if (response.success) {\n        setExtractedQuestions(response.data.questions);\n        setExtractionMetadata(response.data.metadata);\n        setSelectedQuestions(response.data.questions.map((_, index) => index)); // Select all by default\n        setStep(3);\n        message.success(`Successfully extracted ${response.data.questions.length} questions!`);\n      } else {\n        message.error(response.message || 'Failed to extract questions');\n      }\n    } catch (error) {\n      console.error('Extraction error:', error);\n      message.error('An error occurred during extraction');\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  // Handle question selection\n  const handleQuestionSelection = (selectedRowKeys) => {\n    setSelectedQuestions(selectedRowKeys);\n  };\n\n  // Handle question approval and saving\n  const handleApproval = async () => {\n    if (selectedQuestions.length === 0) {\n      message.error('Please select at least one question to approve');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n\n      const questionsToApprove = selectedQuestions.map(index => extractedQuestions[index]);\n\n      console.log('🚀 Approving questions:', {\n        examId,\n        selectedCount: selectedQuestions.length,\n        totalExtracted: extractedQuestions.length,\n        questionsToApprove: questionsToApprove.map(q => ({\n          name: q.name?.substring(0, 50) + '...',\n          type: q.type,\n          hasOptions: !!q.options,\n          correctAnswer: q.correctAnswer\n        }))\n      });\n\n      const response = await approveExtractedQuestions({\n        examId,\n        approvedQuestions: questionsToApprove\n      });\n\n      console.log('📥 Approval response:', response);\n\n      if (response.success) {\n        message.success(`Successfully added ${response.data.savedQuestions} questions to the exam!`);\n        setStep(4);\n        \n        // Notify parent component\n        if (onQuestionsAdded) {\n          onQuestionsAdded(response.data.savedQuestions);\n        }\n      } else {\n        message.error(response.message || 'Failed to approve questions');\n      }\n    } catch (error) {\n      console.error('Approval error:', error);\n      message.error('An error occurred during approval');\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  // Reset modal state\n  const handleClose = () => {\n    setStep(1);\n    setPdfFile(null);\n    setExtractedQuestions([]);\n    setSelectedQuestions([]);\n    setExtractionMetadata(null);\n    form.resetFields();\n    onClose();\n  };\n\n  // Table columns for question review\n  const questionColumns = [\n    {\n      title: 'Question',\n      dataIndex: 'name',\n      key: 'name',\n      width: '40%',\n      render: (text) => (\n        <div className=\"max-w-md\">\n          <p className=\"text-sm font-medium text-gray-900 line-clamp-3\">{text}</p>\n        </div>\n      ),\n    },\n    {\n      title: 'Type',\n      dataIndex: 'type',\n      key: 'type',\n      width: '10%',\n      render: (type) => (\n        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n          type === 'mcq' ? 'bg-blue-100 text-blue-800' :\n          type === 'fill' ? 'bg-green-100 text-green-800' :\n          'bg-purple-100 text-purple-800'\n        }`}>\n          {type.toUpperCase()}\n        </span>\n      ),\n    },\n    {\n      title: 'Correct Answer',\n      dataIndex: 'correctAnswer',\n      key: 'correctAnswer',\n      width: '20%',\n      render: (answer, record) => (\n        <div className=\"text-sm\">\n          {record.type === 'mcq' && record.options ? (\n            <span className=\"font-medium text-green-600\">\n              {answer}: {record.options[answer]}\n            </span>\n          ) : (\n            <span className=\"font-medium text-green-600\">{answer}</span>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: 'Confidence',\n      dataIndex: 'extractionConfidence',\n      key: 'confidence',\n      width: '15%',\n      render: (confidence) => (\n        <div className=\"flex items-center\">\n          <div className={`w-2 h-2 rounded-full mr-2 ${\n            confidence >= 0.8 ? 'bg-green-500' :\n            confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'\n          }`}></div>\n          <span className=\"text-sm\">{Math.round(confidence * 100)}%</span>\n        </div>\n      ),\n    },\n    {\n      title: 'Options',\n      dataIndex: 'options',\n      key: 'options',\n      width: '15%',\n      render: (options, record) => (\n        record.type === 'mcq' && options ? (\n          <div className=\"text-xs space-y-1\">\n            {Object.entries(options).map(([key, value]) => (\n              <div key={key} className=\"truncate\">\n                <span className=\"font-medium\">{key}:</span> {value.substring(0, 30)}...\n              </div>\n            ))}\n          </div>\n        ) : (\n          <span className=\"text-gray-400 text-xs\">N/A</span>\n        )\n      ),\n    },\n  ];\n\n  const rowSelection = {\n    selectedRowKeys: selectedQuestions,\n    onChange: handleQuestionSelection,\n    getCheckboxProps: (record, index) => ({\n      name: `question-${index}`,\n    }),\n  };\n\n  return (\n    <Modal\n      title=\"AI PDF Question Extraction\"\n      open={visible}\n      onCancel={handleClose}\n      width={1200}\n      footer={null}\n      destroyOnClose\n    >\n      <div className=\"space-y-6\">\n        {/* Progress Steps */}\n        <div className=\"flex items-center justify-center space-x-4 mb-8\">\n          {[\n            { step: 1, title: 'Upload PDF', icon: <InboxOutlined /> },\n            { step: 2, title: 'Configure', icon: <FileTextOutlined /> },\n            { step: 3, title: 'Review Questions', icon: <ExclamationCircleOutlined /> },\n            { step: 4, title: 'Complete', icon: <CheckCircleOutlined /> }\n          ].map((item, index) => (\n            <div key={item.step} className=\"flex items-center\">\n              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                step >= item.step \n                  ? 'bg-blue-500 border-blue-500 text-white' \n                  : 'border-gray-300 text-gray-400'\n              }`}>\n                {item.icon}\n              </div>\n              <span className={`ml-2 text-sm font-medium ${\n                step >= item.step ? 'text-blue-600' : 'text-gray-400'\n              }`}>\n                {item.title}\n              </span>\n              {index < 3 && (\n                <div className={`w-8 h-0.5 mx-4 ${\n                  step > item.step ? 'bg-blue-500' : 'bg-gray-300'\n                }`}></div>\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Step 1: Upload PDF */}\n        {step === 1 && (\n          <Card title=\"Upload PDF Document\" className=\"border-2 border-dashed border-gray-300\">\n            <Dragger\n              name=\"pdf\"\n              accept=\".pdf\"\n              customRequest={customRequest}\n              onChange={handleFileUpload}\n              showUploadList={false}\n              className=\"p-8\"\n            >\n              <p className=\"ant-upload-drag-icon\">\n                <InboxOutlined className=\"text-6xl text-blue-500\" />\n              </p>\n              <p className=\"ant-upload-text text-lg font-medium\">\n                Click or drag PDF file to this area to upload\n              </p>\n              <p className=\"ant-upload-hint text-gray-500\">\n                Upload a PDF document containing questions and answers. \n                The AI will automatically extract and structure the questions.\n              </p>\n            </Dragger>\n          </Card>\n        )}\n\n        {/* Step 2: Configure Extraction */}\n        {step === 2 && (\n          <Card title=\"Configure Question Extraction\">\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={handleExtraction}\n              className=\"space-y-4\"\n            >\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <Form.Item\n                  label=\"Subject\"\n                  name=\"subject\"\n                  rules={[{ required: true, message: 'Please select a subject' }]}\n                >\n                  <select className=\"w-full p-2 border border-gray-300 rounded-md\">\n                    <option value=\"\">Select Subject</option>\n                    {getSubjectsForLevel(examData?.level).map((subject) => (\n                      <option key={subject} value={subject}>\n                        {subject}\n                      </option>\n                    ))}\n                  </select>\n                </Form.Item>\n\n                <Form.Item\n                  label=\"Level\"\n                  name=\"level\"\n                  rules={[{ required: true, message: 'Please select a level' }]}\n                >\n                  <select className=\"w-full p-2 border border-gray-300 rounded-md\">\n                    <option value=\"\">Select Level</option>\n                    <option value=\"Primary\">Primary</option>\n                    <option value=\"Secondary\">Secondary</option>\n                    <option value=\"Advance\">Advance</option>\n                  </select>\n                </Form.Item>\n\n                <Form.Item\n                  label=\"Class\"\n                  name=\"class\"\n                  rules={[{ required: true, message: 'Please select a class' }]}\n                >\n                  <select className=\"w-full p-2 border border-gray-300 rounded-md\">\n                    <option value=\"\">Select Class</option>\n                    {getClassesForLevel(examData?.level).map((cls) => (\n                      <option key={cls} value={cls}>\n                        {cls}\n                      </option>\n                    ))}\n                  </select>\n                </Form.Item>\n\n                <Form.Item\n                  label=\"Topic (Optional)\"\n                  name=\"topic\"\n                >\n                  <input \n                    type=\"text\" \n                    placeholder=\"Enter specific topic\"\n                    className=\"w-full p-2 border border-gray-300 rounded-md\"\n                  />\n                </Form.Item>\n              </div>\n\n              <div className=\"flex justify-between pt-4\">\n                <Button onClick={() => setStep(1)}>\n                  Back\n                </Button>\n                <Button \n                  type=\"primary\" \n                  htmlType=\"submit\"\n                  loading={loading}\n                  className=\"bg-blue-500 hover:bg-blue-600\"\n                >\n                  Extract Questions\n                </Button>\n              </div>\n            </Form>\n          </Card>\n        )}\n\n        {/* Step 3: Review Questions */}\n        {step === 3 && (\n          <Card title={`Review Extracted Questions (${extractedQuestions.length} found)`}>\n            <div className=\"mb-4 p-4 bg-blue-50 rounded-lg\">\n              <p className=\"text-sm text-blue-800\">\n                <strong>Extraction Summary:</strong> Found {extractedQuestions.length} questions from your PDF. \n                Review and select the questions you want to add to the exam.\n              </p>\n              {extractionMetadata && (\n                <p className=\"text-xs text-blue-600 mt-1\">\n                  Processed {extractionMetadata.textLength} characters of text content.\n                </p>\n              )}\n            </div>\n\n            <Table\n              rowSelection={rowSelection}\n              columns={questionColumns}\n              dataSource={extractedQuestions.map((q, index) => ({ ...q, key: index }))}\n              pagination={{ pageSize: 5 }}\n              scroll={{ x: 1000 }}\n              size=\"small\"\n            />\n\n            <div className=\"flex justify-between pt-4\">\n              <Button onClick={() => setStep(2)}>\n                Back\n              </Button>\n              <div className=\"space-x-2\">\n                <span className=\"text-sm text-gray-600\">\n                  {selectedQuestions.length} of {extractedQuestions.length} questions selected\n                </span>\n                <Button \n                  type=\"primary\" \n                  onClick={handleApproval}\n                  disabled={selectedQuestions.length === 0}\n                  loading={loading}\n                  className=\"bg-green-500 hover:bg-green-600\"\n                >\n                  Approve & Add Questions\n                </Button>\n              </div>\n            </div>\n          </Card>\n        )}\n\n        {/* Step 4: Success */}\n        {step === 4 && (\n          <Card className=\"text-center\">\n            <div className=\"py-8\">\n              <CheckCircleOutlined className=\"text-6xl text-green-500 mb-4\" />\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Questions Added Successfully!\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                {selectedQuestions.length} questions have been extracted from your PDF and added to the exam.\n              </p>\n              <Button \n                type=\"primary\" \n                onClick={handleClose}\n                className=\"bg-blue-500 hover:bg-blue-600\"\n              >\n                Done\n              </Button>\n            </div>\n          </Card>\n        )}\n\n        {/* Loading Overlay */}\n        {loading && (\n          <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\">\n            <Spin size=\"large\" />\n          </div>\n        )}\n      </div>\n    </Modal>\n  );\n}\n\nexport default PDFQuestionExtraction;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AACxF,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,mBAAmB;AACnH,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,iCAAiC;AACpG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAM;EAAEC;AAAQ,CAAC,GAAGnB,MAAM;AAE1B,SAASoB,qBAAqBA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAiB,CAAC,EAAE;EAAAC,EAAA;EACvF,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM8C,eAAe,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;EAC9H,MAAMC,iBAAiB,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC;EAChL,MAAMC,eAAe,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC;EAE9K,MAAMC,mBAAmB,GAAIC,KAAK,IAAK;IACrC,QAAQA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,WAAW,CAAC,CAAC;MAC1B,KAAK,SAAS;QAAE,OAAOL,eAAe;MACtC,KAAK,WAAW;QAAE,OAAOC,iBAAiB;MAC1C,KAAK,SAAS;QAAE,OAAOC,eAAe;MACtC;QAAS,OAAOF,eAAe;IACjC;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAIF,KAAK,IAAK;IACpC,QAAQA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,WAAW,CAAC,CAAC;MAC1B,KAAK,SAAS;QAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC1D,KAAK,WAAW;QAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjE,KAAK,SAAS;QAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC3C;QAAS,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACrD;EACF,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI2B,QAAQ,EAAE;MACZI,IAAI,CAACqB,cAAc,CAAC;QAClBC,OAAO,EAAE1B,QAAQ,CAAC0B,OAAO,IAAI1B,QAAQ,CAAC2B,QAAQ;QAC9CL,KAAK,EAAEtB,QAAQ,CAACsB,KAAK;QACrBM,KAAK,EAAE5B,QAAQ,CAAC4B,KAAK;QACrBC,KAAK,EAAE7B,QAAQ,CAAC2B;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3B,QAAQ,EAAEI,IAAI,CAAC,CAAC;;EAEpB;EACA,MAAM0B,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAM;MAAEC,MAAM;MAAEC;IAAc,CAAC,GAAGF,IAAI,CAACG,IAAI;IAE3C,IAAIF,MAAM,KAAK,MAAM,IAAIC,aAAa,EAAE;MACtCxB,UAAU,CAACwB,aAAa,IAAIF,IAAI,CAACG,IAAI,CAAC;MACtC3D,OAAO,CAAC4D,OAAO,CAAC,gCAAgC,CAAC;MACjD5B,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM,IAAIyB,MAAM,KAAK,OAAO,EAAE;MAC7BzD,OAAO,CAAC6D,KAAK,CAAC,mBAAmB,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAC;IAAEH,IAAI;IAAEI;EAAU,CAAC,KAAK;IAC7CC,UAAU,CAAC,MAAM;MACfD,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI,CAACjC,OAAO,EAAE;MACZjC,OAAO,CAAC6D,KAAK,CAAC,gCAAgC,CAAC;MAC/C;IACF;IAEA,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBd,QAAQ,CAACd,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMqD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEpC,OAAO,CAAC;MAC/BkC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE7C,MAAM,CAAC;MACjC2C,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,MAAM,CAACf,OAAO,CAAC;MAC1CgB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACnB,KAAK,CAAC;MACtCoB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACb,KAAK,CAAC;MACtCc,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACZ,KAAK,IAAIY,MAAM,CAACf,OAAO,CAAC;MAExDmB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMC,QAAQ,GAAG,MAAMzD,uBAAuB,CAACoD,QAAQ,CAAC;MAExD,IAAIK,QAAQ,CAACZ,OAAO,EAAE;QACpBxB,qBAAqB,CAACoC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC;QAC9ClC,qBAAqB,CAACgC,QAAQ,CAACC,IAAI,CAACE,QAAQ,CAAC;QAC7CrC,oBAAoB,CAACkC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKA,KAAK,CAAC,CAAC,CAAC,CAAC;QACxE9C,OAAO,CAAC,CAAC,CAAC;QACVhC,OAAO,CAAC4D,OAAO,CAAE,0BAAyBY,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACK,MAAO,aAAY,CAAC;MACxF,CAAC,MAAM;QACL/E,OAAO,CAAC6D,KAAK,CAACW,QAAQ,CAACxE,OAAO,IAAI,6BAA6B,CAAC;MAClE;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC7D,OAAO,CAAC6D,KAAK,CAAC,qCAAqC,CAAC;IACtD,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;MACjBd,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmE,uBAAuB,GAAIC,eAAe,IAAK;IACnD3C,oBAAoB,CAAC2C,eAAe,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI7C,iBAAiB,CAAC0C,MAAM,KAAK,CAAC,EAAE;MAClC/E,OAAO,CAAC6D,KAAK,CAAC,gDAAgD,CAAC;MAC/D;IACF;IAEA,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBd,QAAQ,CAACd,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMqE,kBAAkB,GAAG9C,iBAAiB,CAACuC,GAAG,CAACE,KAAK,IAAI3C,kBAAkB,CAAC2C,KAAK,CAAC,CAAC;MAEpFR,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;QACrC/C,MAAM;QACN4D,aAAa,EAAE/C,iBAAiB,CAAC0C,MAAM;QACvCM,cAAc,EAAElD,kBAAkB,CAAC4C,MAAM;QACzCI,kBAAkB,EAAEA,kBAAkB,CAACP,GAAG,CAACU,CAAC;UAAA,IAAAC,OAAA;UAAA,OAAK;YAC/CC,IAAI,EAAE,EAAAD,OAAA,GAAAD,CAAC,CAACE,IAAI,cAAAD,OAAA,uBAANA,OAAA,CAAQE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK;YACtCC,IAAI,EAAEJ,CAAC,CAACI,IAAI;YACZC,UAAU,EAAE,CAAC,CAACL,CAAC,CAACM,OAAO;YACvBC,aAAa,EAAEP,CAAC,CAACO;UACnB,CAAC;QAAA,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMrB,QAAQ,GAAG,MAAMxD,yBAAyB,CAAC;QAC/CQ,MAAM;QACNsE,iBAAiB,EAAEX;MACrB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;MAE9C,IAAIA,QAAQ,CAACZ,OAAO,EAAE;QACpB5D,OAAO,CAAC4D,OAAO,CAAE,sBAAqBY,QAAQ,CAACC,IAAI,CAACsB,cAAe,yBAAwB,CAAC;QAC5F/D,OAAO,CAAC,CAAC,CAAC;;QAEV;QACA,IAAIN,gBAAgB,EAAE;UACpBA,gBAAgB,CAAC8C,QAAQ,CAACC,IAAI,CAACsB,cAAc,CAAC;QAChD;MACF,CAAC,MAAM;QACL/F,OAAO,CAAC6D,KAAK,CAACW,QAAQ,CAACxE,OAAO,IAAI,6BAA6B,CAAC;MAClE;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC7D,OAAO,CAAC6D,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;MACjBd,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmF,WAAW,GAAGA,CAAA,KAAM;IACxBhE,OAAO,CAAC,CAAC,CAAC;IACVE,UAAU,CAAC,IAAI,CAAC;IAChBE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BX,IAAI,CAACoE,WAAW,CAAC,CAAC;IAClB1E,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAM2E,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,UAAU;IACjBC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGC,IAAI,iBACXrF,OAAA;MAAKsF,SAAS,EAAC,UAAU;MAAAC,QAAA,eACvBvF,OAAA;QAAGsF,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAEF;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE;EAET,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGb,IAAI,iBACXvE,OAAA;MAAMsF,SAAS,EAAG,8CAChBf,IAAI,KAAK,KAAK,GAAG,2BAA2B,GAC5CA,IAAI,KAAK,MAAM,GAAG,6BAA6B,GAC/C,+BACD,EAAE;MAAAgB,QAAA,EACAhB,IAAI,CAACqB,WAAW,CAAC;IAAC;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAEV,CAAC,EACD;IACEX,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACS,MAAM,EAAEC,MAAM,kBACrB9F,OAAA;MAAKsF,SAAS,EAAC,SAAS;MAAAC,QAAA,EACrBO,MAAM,CAACvB,IAAI,KAAK,KAAK,IAAIuB,MAAM,CAACrB,OAAO,gBACtCzE,OAAA;QAAMsF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GACzCM,MAAM,EAAC,IAAE,EAACC,MAAM,CAACrB,OAAO,CAACoB,MAAM,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,gBAEP3F,OAAA;QAAMsF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAEM;MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAC5D;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACEX,KAAK,EAAE,YAAY;IACnBC,SAAS,EAAE,sBAAsB;IACjCC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGW,UAAU,iBACjB/F,OAAA;MAAKsF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvF,OAAA;QAAKsF,SAAS,EAAG,6BACfS,UAAU,IAAI,GAAG,GAAG,cAAc,GAClCA,UAAU,IAAI,GAAG,GAAG,eAAe,GAAG,YACvC;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACV3F,OAAA;QAAMsF,SAAS,EAAC,SAAS;QAAAC,QAAA,GAAES,IAAI,CAACC,KAAK,CAACF,UAAU,GAAG,GAAG,CAAC,EAAC,GAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D;EAET,CAAC,EACD;IACEX,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACX,OAAO,EAAEqB,MAAM,KACtBA,MAAM,CAACvB,IAAI,KAAK,KAAK,IAAIE,OAAO,gBAC9BzE,OAAA;MAAKsF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/BW,MAAM,CAACC,OAAO,CAAC1B,OAAO,CAAC,CAAChB,GAAG,CAAC,CAAC,CAACyB,GAAG,EAAEkB,KAAK,CAAC,kBACxCpG,OAAA;QAAesF,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACjCvF,OAAA;UAAMsF,SAAS,EAAC,aAAa;UAAAC,QAAA,GAAEL,GAAG,EAAC,GAAC;QAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,KAAC,EAACS,KAAK,CAAC9B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACtE;MAAA,GAFUY,GAAG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAEN3F,OAAA;MAAMsF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAC;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAGvD,CAAC,CACF;EAED,MAAMU,YAAY,GAAG;IACnBvC,eAAe,EAAE5C,iBAAiB;IAClCoF,QAAQ,EAAEzC,uBAAuB;IACjC0C,gBAAgB,EAAEA,CAACT,MAAM,EAAEnC,KAAK,MAAM;MACpCU,IAAI,EAAG,YAAWV,KAAM;IAC1B,CAAC;EACH,CAAC;EAED,oBACE3D,OAAA,CAACb,KAAK;IACJ6F,KAAK,EAAC,4BAA4B;IAClCwB,IAAI,EAAErG,OAAQ;IACdsG,QAAQ,EAAE5B,WAAY;IACtBM,KAAK,EAAE,IAAK;IACZuB,MAAM,EAAE,IAAK;IACbC,cAAc;IAAApB,QAAA,eAEdvF,OAAA;MAAKsF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBvF,OAAA;QAAKsF,SAAS,EAAC,iDAAiD;QAAAC,QAAA,EAC7D,CACC;UAAE3E,IAAI,EAAE,CAAC;UAAEoE,KAAK,EAAE,YAAY;UAAE4B,IAAI,eAAE5G,OAAA,CAACX,aAAa;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE,CAAC,EACzD;UAAE/E,IAAI,EAAE,CAAC;UAAEoE,KAAK,EAAE,WAAW;UAAE4B,IAAI,eAAE5G,OAAA,CAACV,gBAAgB;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE,CAAC,EAC3D;UAAE/E,IAAI,EAAE,CAAC;UAAEoE,KAAK,EAAE,kBAAkB;UAAE4B,IAAI,eAAE5G,OAAA,CAACR,yBAAyB;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE,CAAC,EAC3E;UAAE/E,IAAI,EAAE,CAAC;UAAEoE,KAAK,EAAE,UAAU;UAAE4B,IAAI,eAAE5G,OAAA,CAACT,mBAAmB;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE,CAAC,CAC9D,CAAClC,GAAG,CAAC,CAACoD,IAAI,EAAElD,KAAK,kBAChB3D,OAAA;UAAqBsF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChDvF,OAAA;YAAKsF,SAAS,EAAG,oEACf1E,IAAI,IAAIiG,IAAI,CAACjG,IAAI,GACb,wCAAwC,GACxC,+BACL,EAAE;YAAA2E,QAAA,EACAsB,IAAI,CAACD;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACN3F,OAAA;YAAMsF,SAAS,EAAG,4BAChB1E,IAAI,IAAIiG,IAAI,CAACjG,IAAI,GAAG,eAAe,GAAG,eACvC,EAAE;YAAA2E,QAAA,EACAsB,IAAI,CAAC7B;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,EACNhC,KAAK,GAAG,CAAC,iBACR3D,OAAA;YAAKsF,SAAS,EAAG,kBACf1E,IAAI,GAAGiG,IAAI,CAACjG,IAAI,GAAG,aAAa,GAAG,aACpC;UAAE;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACV;QAAA,GAjBOkB,IAAI,CAACjG,IAAI;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL/E,IAAI,KAAK,CAAC,iBACTZ,OAAA,CAAChB,IAAI;QAACgG,KAAK,EAAC,qBAAqB;QAACM,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eAClFvF,OAAA,CAACC,OAAO;UACNoE,IAAI,EAAC,KAAK;UACVyC,MAAM,EAAC,MAAM;UACbnE,aAAa,EAAEA,aAAc;UAC7B2D,QAAQ,EAAElE,gBAAiB;UAC3B2E,cAAc,EAAE,KAAM;UACtBzB,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAEfvF,OAAA;YAAGsF,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjCvF,OAAA,CAACX,aAAa;cAACiG,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACJ3F,OAAA;YAAGsF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3F,OAAA;YAAGsF,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAG7C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACP,EAGA/E,IAAI,KAAK,CAAC,iBACTZ,OAAA,CAAChB,IAAI;QAACgG,KAAK,EAAC,+BAA+B;QAAAO,QAAA,eACzCvF,OAAA,CAACpB,IAAI;UACH8B,IAAI,EAAEA,IAAK;UACXsG,MAAM,EAAC,UAAU;UACjBC,QAAQ,EAAEnE,gBAAiB;UAC3BwC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErBvF,OAAA;YAAKsF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDvF,OAAA,CAACpB,IAAI,CAACsI,IAAI;cACRC,KAAK,EAAC,SAAS;cACf9C,IAAI,EAAC,SAAS;cACd+C,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAExI,OAAO,EAAE;cAA0B,CAAC,CAAE;cAAA0G,QAAA,eAEhEvF,OAAA;gBAAQsF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC9DvF,OAAA;kBAAQoG,KAAK,EAAC,EAAE;kBAAAb,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvChE,mBAAmB,CAACrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAAC,CAAC6B,GAAG,CAAEzB,OAAO,iBAChDhC,OAAA;kBAAsBoG,KAAK,EAAEpE,OAAQ;kBAAAuD,QAAA,EAClCvD;gBAAO,GADGA,OAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZ3F,OAAA,CAACpB,IAAI,CAACsI,IAAI;cACRC,KAAK,EAAC,OAAO;cACb9C,IAAI,EAAC,OAAO;cACZ+C,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAExI,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAA0G,QAAA,eAE9DvF,OAAA;gBAAQsF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC9DvF,OAAA;kBAAQoG,KAAK,EAAC,EAAE;kBAAAb,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA;kBAAQoG,KAAK,EAAC,SAAS;kBAAAb,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC3F,OAAA;kBAAQoG,KAAK,EAAC,WAAW;kBAAAb,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C3F,OAAA;kBAAQoG,KAAK,EAAC,SAAS;kBAAAb,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZ3F,OAAA,CAACpB,IAAI,CAACsI,IAAI;cACRC,KAAK,EAAC,OAAO;cACb9C,IAAI,EAAC,OAAO;cACZ+C,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAExI,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAA0G,QAAA,eAE9DvF,OAAA;gBAAQsF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC9DvF,OAAA;kBAAQoG,KAAK,EAAC,EAAE;kBAAAb,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACrC7D,kBAAkB,CAACxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,KAAK,CAAC,CAAC6B,GAAG,CAAE6D,GAAG,iBAC3CtH,OAAA;kBAAkBoG,KAAK,EAAEkB,GAAI;kBAAA/B,QAAA,EAC1B+B;gBAAG,GADOA,GAAG;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZ3F,OAAA,CAACpB,IAAI,CAACsI,IAAI;cACRC,KAAK,EAAC,kBAAkB;cACxB9C,IAAI,EAAC,OAAO;cAAAkB,QAAA,eAEZvF,OAAA;gBACEuE,IAAI,EAAC,MAAM;gBACXgD,WAAW,EAAC,sBAAsB;gBAClCjC,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN3F,OAAA;YAAKsF,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCvF,OAAA,CAACjB,MAAM;cAACyI,OAAO,EAAEA,CAAA,KAAM3G,OAAO,CAAC,CAAC,CAAE;cAAA0E,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3F,OAAA,CAACjB,MAAM;cACLwF,IAAI,EAAC,SAAS;cACdkD,QAAQ,EAAC,QAAQ;cACjBnG,OAAO,EAAEA,OAAQ;cACjBgE,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,EAGA/E,IAAI,KAAK,CAAC,iBACTZ,OAAA,CAAChB,IAAI;QAACgG,KAAK,EAAG,+BAA8BhE,kBAAkB,CAAC4C,MAAO,SAAS;QAAA2B,QAAA,gBAC7EvF,OAAA;UAAKsF,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7CvF,OAAA;YAAGsF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCvF,OAAA;cAAAuF,QAAA,EAAQ;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,WAAO,EAAC3E,kBAAkB,CAAC4C,MAAM,EAAC,wFAExE;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EACHvE,kBAAkB,iBACjBpB,OAAA;YAAGsF,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,YAC9B,EAACnE,kBAAkB,CAACsG,UAAU,EAAC,8BAC3C;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN3F,OAAA,CAACd,KAAK;UACJmH,YAAY,EAAEA,YAAa;UAC3BsB,OAAO,EAAE5C,eAAgB;UACzB6C,UAAU,EAAE5G,kBAAkB,CAACyC,GAAG,CAAC,CAACU,CAAC,EAAER,KAAK,MAAM;YAAE,GAAGQ,CAAC;YAAEe,GAAG,EAAEvB;UAAM,CAAC,CAAC,CAAE;UACzEkE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAC5BC,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAK,CAAE;UACpBC,IAAI,EAAC;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEF3F,OAAA;UAAKsF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCvF,OAAA,CAACjB,MAAM;YAACyI,OAAO,EAAEA,CAAA,KAAM3G,OAAO,CAAC,CAAC,CAAE;YAAA0E,QAAA,EAAC;UAEnC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3F,OAAA;YAAKsF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvF,OAAA;cAAMsF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GACpCrE,iBAAiB,CAAC0C,MAAM,EAAC,MAAI,EAAC5C,kBAAkB,CAAC4C,MAAM,EAAC,qBAC3D;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3F,OAAA,CAACjB,MAAM;cACLwF,IAAI,EAAC,SAAS;cACdiD,OAAO,EAAEzD,cAAe;cACxBmE,QAAQ,EAAEhH,iBAAiB,CAAC0C,MAAM,KAAK,CAAE;cACzCtC,OAAO,EAAEA,OAAQ;cACjBgE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,EAGA/E,IAAI,KAAK,CAAC,iBACTZ,OAAA,CAAChB,IAAI;QAACsG,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC3BvF,OAAA;UAAKsF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBvF,OAAA,CAACT,mBAAmB;YAAC+F,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE3F,OAAA;YAAIsF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3F,OAAA;YAAGsF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAC9BrE,iBAAiB,CAAC0C,MAAM,EAAC,qEAC5B;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ3F,OAAA,CAACjB,MAAM;YACLwF,IAAI,EAAC,SAAS;YACdiD,OAAO,EAAE3C,WAAY;YACrBS,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAC1C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,EAGArE,OAAO,iBACNtB,OAAA;QAAKsF,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5FvF,OAAA,CAACZ,IAAI;UAAC6I,IAAI,EAAC;QAAO;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ;AAACnF,EAAA,CAveQN,qBAAqB;EAAA,QACXT,WAAW,EACbb,IAAI,CAAC+B,OAAO;AAAA;AAAAwH,EAAA,GAFpBjI,qBAAqB;AAye9B,eAAeA,qBAAqB;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}