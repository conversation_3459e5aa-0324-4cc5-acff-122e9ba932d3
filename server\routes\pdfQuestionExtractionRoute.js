const express = require("express");
const router = express.Router();
const multer = require("multer");
const authMiddleware = require("../middlewares/authMiddleware");
const PDFQuestionExtractionService = require("../services/pdfQuestionExtractionService");
const Question = require("../models/questionModel");
const Exam = require("../models/examModel");

// Configure multer for PDF upload
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'), false);
    }
  }
});

// Extract questions from PDF
router.post("/extract-questions", authMiddleware, upload.single('pdf'), async (req, res) => {
  try {
    console.log('📄 PDF question extraction request received');
    
    // Validate file upload
    if (!req.file) {
      return res.status(400).send({
        message: "No PDF file uploaded",
        success: false,
      });
    }

    // Validate exam configuration
    const { examId, subject, level, class: className, topic } = req.body;
    
    if (!examId || !subject || !level || !className) {
      return res.status(400).send({
        message: "Missing required exam configuration (examId, subject, level, class)",
        success: false,
      });
    }

    // Verify exam exists
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    console.log(`📋 Extracting questions for exam: ${exam.name}`);
    console.log(`📄 PDF file size: ${req.file.size} bytes`);

    // Prepare exam configuration
    const examConfig = {
      examId,
      subject,
      level,
      class: className,
      topic: topic || exam.category || 'General'
    };

    // Initialize PDF extraction service
    const extractionService = new PDFQuestionExtractionService();
    
    // Extract questions from PDF
    const result = await extractionService.extractQuestionsFromPDF(req.file.buffer, examConfig);
    
    res.send({
      message: `Successfully extracted ${result.questions.length} questions from PDF`,
      success: true,
      data: {
        questions: result.questions,
        metadata: result.metadata,
        examInfo: {
          id: exam._id,
          name: exam.name,
          subject: exam.subject,
          level: exam.level,
          class: exam.class
        }
      }
    });

  } catch (error) {
    console.error('❌ PDF question extraction error:', error);
    
    let errorMessage = "Failed to extract questions from PDF";
    if (error.message.includes('pdf-parse')) {
      errorMessage = "Invalid or corrupted PDF file";
    } else if (error.message.includes('OpenAI')) {
      errorMessage = "AI service temporarily unavailable";
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(500).send({
      message: errorMessage,
      success: false,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Approve and save extracted questions to exam
router.post("/approve-questions", authMiddleware, async (req, res) => {
  try {
    console.log('✅ Question approval request received');
    
    const { examId, approvedQuestions } = req.body;
    
    if (!examId || !approvedQuestions || !Array.isArray(approvedQuestions)) {
      return res.status(400).send({
        message: "Missing required data (examId, approvedQuestions array)",
        success: false,
      });
    }

    // Verify exam exists
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    console.log(`📝 Approving ${approvedQuestions.length} questions for exam: ${exam.name}`);

    const savedQuestions = [];
    const errors = [];

    // Process each approved question
    for (let i = 0; i < approvedQuestions.length; i++) {
      try {
        const questionData = approvedQuestions[i];
        
        // Ensure exam reference is set
        questionData.exam = examId;
        
        // Create and save question
        const newQuestion = new Question(questionData);
        const savedQuestion = await newQuestion.save();
        
        // Add question to exam
        exam.questions.push(savedQuestion._id);
        
        savedQuestions.push(savedQuestion);
        console.log(`✅ Question ${i + 1} saved successfully`);
        
      } catch (error) {
        console.error(`❌ Error saving question ${i + 1}:`, error);
        errors.push({
          questionIndex: i,
          error: error.message
        });
      }
    }

    // Save exam with new questions
    await exam.save();

    console.log(`🎉 Successfully saved ${savedQuestions.length} questions to exam`);
    console.log(`📊 Exam now has ${exam.questions.length} total questions`);

    // Verify questions were saved by re-fetching the exam
    const updatedExam = await Exam.findById(examId).populate("questions");
    console.log(`✅ Verification: Exam has ${updatedExam.questions.length} questions after save`);

    res.send({
      message: `Successfully approved and saved ${savedQuestions.length} questions`,
      success: true,
      data: {
        savedQuestions: savedQuestions.length,
        totalQuestions: exam.questions.length,
        errors: errors.length > 0 ? errors : undefined,
        examInfo: {
          id: exam._id,
          name: exam.name,
          questionsCount: exam.questions.length
        }
      }
    });

  } catch (error) {
    console.error('❌ Question approval error:', error);
    res.status(500).send({
      message: "Failed to approve questions: " + error.message,
      success: false,
    });
  }
});

// Get extraction history for an exam
router.get("/extraction-history/:examId", authMiddleware, async (req, res) => {
  try {
    const { examId } = req.params;
    
    // Find questions that were extracted from PDF for this exam
    const extractedQuestions = await Question.find({
      exam: examId,
      generationSource: 'pdf_extraction',
      isAIGenerated: true
    }).select('name type extractionConfidence extractionMetadata createdAt');

    res.send({
      message: "Extraction history retrieved successfully",
      success: true,
      data: {
        extractedQuestions,
        totalExtracted: extractedQuestions.length
      }
    });

  } catch (error) {
    console.error('❌ Error getting extraction history:', error);
    res.status(500).send({
      message: "Failed to get extraction history: " + error.message,
      success: false,
    });
  }
});

module.exports = router;
