import { axiosInstance } from "./index";

// Extract questions from PDF
export const extractQuestionsFromPDF = async (formData) => {
  try {
    const response = await axiosInstance.post('/api/pdf-extraction/extract-questions', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    return error.response?.data || { 
      success: false, 
      message: 'Network error occurred' 
    };
  }
};

// Approve and save extracted questions
export const approveExtractedQuestions = async (payload) => {
  try {
    const response = await axiosInstance.post('/api/pdf-extraction/approve-questions', payload);
    return response.data;
  } catch (error) {
    return error.response?.data || { 
      success: false, 
      message: 'Network error occurred' 
    };
  }
};

// Get extraction history for an exam
export const getExtractionHistory = async (examId) => {
  try {
    const response = await axiosInstance.get(`/api/pdf-extraction/extraction-history/${examId}`);
    return response.data;
  } catch (error) {
    return error.response?.data || { 
      success: false, 
      message: 'Network error occurred' 
    };
  }
};
