{"ast": null, "code": "var _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\nconst AdminProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n  useEffect(() => {\n    console.log(\"AdminProtectedRoute: User state changed\", {\n      user: user ? {\n        name: user.name,\n        isAdmin: user.isAdmin\n      } : null\n    });\n\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      console.log(\"AdminProtectedRoute: Non-admin user detected, redirecting to user hub\");\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    return null;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    return null;\n  }\n\n  // If user is admin, render the children\n  return children;\n};\n_s(AdminProtectedRoute, \"mL2t1COiLoZ5+Kl1K33h74o9/R8=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AdminProtectedRoute;\nexport default AdminProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminProtectedRoute\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useNavigate", "message", "AdminProtectedRoute", "children", "_s", "user", "state", "navigate", "console", "log", "name", "isAdmin", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/AdminProtectedRoute.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\n\nconst AdminProtectedRoute = ({ children }) => {\n  const { user } = useSelector((state) => state.user);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    console.log(\"AdminProtectedRoute: User state changed\", {\n      user: user ? { name: user.name, isAdmin: user.isAdmin } : null\n    });\n\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      console.log(\"AdminProtectedRoute: Non-admin user detected, redirecting to user hub\");\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    return null;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    return null;\n  }\n\n  // If user is admin, render the children\n  return children;\n};\n\nexport default AdminProtectedRoute;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAE9B,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM;IAAEC;EAAK,CAAC,GAAGN,WAAW,CAAEO,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdU,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDJ,IAAI,EAAEA,IAAI,GAAG;QAAEK,IAAI,EAAEL,IAAI,CAACK,IAAI;QAAEC,OAAO,EAAEN,IAAI,CAACM;MAAQ,CAAC,GAAG;IAC5D,CAAC,CAAC;;IAEF;IACA,IAAIN,IAAI,IAAI,CAACA,IAAI,CAACM,OAAO,EAAE;MACzBH,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;MACpFR,OAAO,CAACW,KAAK,CAAC,2CAA2C,CAAC;MAC1DL,QAAQ,CAAC,WAAW,CAAC;IACvB;EACF,CAAC,EAAE,CAACF,IAAI,EAAEE,QAAQ,CAAC,CAAC;;EAEpB;EACA,IAAI,CAACF,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAACA,IAAI,CAACM,OAAO,EAAE;IACjB,OAAO,IAAI;EACb;;EAEA;EACA,OAAOR,QAAQ;AACjB,CAAC;AAACC,EAAA,CA7BIF,mBAAmB;EAAA,QACNH,WAAW,EACXC,WAAW;AAAA;AAAAa,EAAA,GAFxBX,mBAAmB;AA+BzB,eAAeA,mBAAmB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}