const router = require("express").Router();
const Exam = require("../models/examModel");
const authMiddleware = require("../middlewares/authMiddleware");
const Question = require("../models/questionModel");
const NotificationService = require("../services/notificationService");
const AWS = require("aws-sdk");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/userModel");
const multer = require("multer");
const Report = require("../models/reportModel");
// Configure Multer Memory Storage
const storage = multer.memoryStorage();
const upload = multer({ storage });
const mongoose = require("mongoose");

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

// add exam

router.post("/add", authMiddleware, async (req, res) => {
  try {
    // check if exam already exists
    const examExists = await Exam.findOne({ name: req.body.name });
    if (examExists) {
      return res
        .status(200)
        .send({ message: "Exam already exists", success: false });
    }
    req.body.questions = [];
    const newExam = new Exam(req.body);
    await newExam.save();

    // Send notification about new exam
    try {
      console.log('🔔 Sending new exam notification for:', newExam.name);
      await NotificationService.notifyNewExam(newExam);
      console.log('✅ New exam notification sent successfully');
    } catch (notifError) {
      console.error('❌ Error sending new exam notification:', notifError);
      // Don't fail the exam creation if notification fails
    }

    res.send({
      message: "Exam added successfully",
      success: true,
      data: newExam, // Return the created exam data
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

router.post("/get-all-exams", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    console.log("🔍 Fetching exams for user ID:", userId, "at", new Date().toISOString());

    if (!userId) {
      return res.status(400).send({
        message: "User ID is required",
        success: false,
      });
    }

    const user = await User.findById(userId);

    if (!user) {
      console.log("❌ User not found with ID:", userId);
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    console.log("✅ User found:", {
      id: user._id,
      name: user.name,
      level: user.level,
      isAdmin: user.isAdmin
    });

    if (user.isAdmin) {
      const exams = await Exam.find().populate('questions');
      return res.send({
        message: "Exams fetched successfully (Admin)",
        data: exams,
        success: true,
      });
    }

    // Safely get user level with proper null checks
    const userLevel = (user && user.level) ? user.level : 'Primary';

    let exams;

    // Fetch exams based on user's level and populate questions
    if (userLevel === "Secondary") {
      exams = await Exam.find({ level: "Secondary" }).populate('questions');
    } else if (userLevel === "Advance") {
      exams = await Exam.find({ level: "Advance" }).populate('questions');
    } else {
      // Primary level - include only primary level exams
      exams = await Exam.find({ level: "Primary" }).populate('questions');
    }

    // Filter out exams with no questions for regular users
    if (!user.isAdmin) {
      const beforeFilterCount = exams.length;
      exams = exams.filter(exam => exam.questions && exam.questions.length > 0);
      const afterFilterCount = exams.length;

      console.log(`📊 Filtering results: ${beforeFilterCount} → ${afterFilterCount} exams (${beforeFilterCount - afterFilterCount} filtered out for having no questions)`);

      // Log which exams were filtered out
      const filteredOutExams = await Exam.find({ level: userLevel }).populate('questions');
      filteredOutExams.forEach(exam => {
        if (!exam.questions || exam.questions.length === 0) {
          console.log(`❌ Filtered out: ${exam.name} (${exam.questions.length} questions)`);
        }
      });
    }

    // Debug: Log exam information
    console.log("=== EXAMS FETCH DEBUG ===");
    console.log(`User Level: ${userLevel}`);
    console.log(`User ID: ${userId}`);
    console.log(`User Admin: ${user ? user.isAdmin : 'N/A'}`);
    console.log(`User Class: ${user ? user.class : 'N/A'}`);
    console.log(`Total exams found before filtering: ${exams.length}`);

    // Log recent exams (last 5) to help debug new exam visibility
    const recentExams = exams.slice(0, 5);
    console.log("📅 Recent exams (last 5):");
    recentExams.forEach((exam, index) => {
      console.log(`  ${index + 1}. ${exam.name} - Level: ${exam.level}, Class: ${exam.class}, Questions: ${exam.questions.length}, Created: ${exam.createdAt}`);
    });

    // Log all exams before filtering
    exams.forEach(exam => {
      console.log(`- ${exam.name}: Level=${exam.level}, Class=${exam.class}, Questions=${exam.questions.length}`);
    });

    // Check specifically for P7SC-DZ
    const p7scExam = exams.find(exam => exam.name.includes('P7SC-DZ'));
    if (p7scExam) {
      console.log("🎯 FOUND P7SC-DZ EXAM:");
      console.log(`  - Name: ${p7scExam.name}`);
      console.log(`  - Level: ${p7scExam.level} (User Level: ${userLevel})`);
      console.log(`  - Class: ${p7scExam.class} (User Class: ${user ? user.class : 'N/A'})`);
      console.log(`  - Questions: ${p7scExam.questions.length}`);
      console.log(`  - Level Match: ${p7scExam.level === userLevel ? 'YES' : 'NO'}`);
      console.log(`  - Has Questions: ${p7scExam.questions.length > 0 ? 'YES' : 'NO'}`);
      console.log(`  - Will be filtered out: ${p7scExam.questions.length === 0 ? 'YES (No Questions)' : 'NO'}`);

      // Check if exam matches user's level exactly
      if (p7scExam.level !== userLevel) {
        console.log(`  ⚠️  LEVEL MISMATCH: Exam level "${p7scExam.level}" != User level "${userLevel}"`);
      }
    } else {
      console.log("❌ P7SC-DZ EXAM NOT FOUND in fetched exams");

      // Check if it exists in database but not fetched due to level mismatch
      const allP7SCExams = await Exam.find({ name: { $regex: 'P7SC-DZ', $options: 'i' } }).populate('questions');
      if (allP7SCExams.length > 0) {
        console.log("🔍 P7SC-DZ EXISTS in database but not fetched:");
        allP7SCExams.forEach(exam => {
          console.log(`  - ${exam.name}: Level=${exam.level}, Questions=${exam.questions.length}`);
          console.log(`  - Reason not fetched: Level mismatch (${exam.level} != ${userLevel})`);
        });
      }
    }

    console.log("========================");

    res.send({
      message: "Exams fetched successfully",
      data: exams,
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

router.post("/get-exam-by-id", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const examId = req.body.examId;

    // Validate examId
    if (!examId || examId === 'undefined' || examId === 'null') {
      return res.status(400).send({
        message: "Invalid exam ID provided",
        success: false,
      });
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(examId)) {
      return res.status(400).send({
        message: "Invalid exam ID format",
        success: false,
      });
    }

    // Get user to check their level
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    const exam = await Exam.findById(examId).populate("questions");
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    // Debug: Check exam questions
    console.log("=== GET EXAM DEBUG ===");
    console.log("Exam ID:", examId);
    console.log("Exam name:", exam.name);
    console.log("Questions count:", exam.questions.length);
    console.log("User level:", user.level);
    console.log("Exam level:", exam.level);

    if (exam.questions.length > 0) {
      console.log("First question:", {
        id: exam.questions[0]._id,
        name: exam.questions[0].name?.substring(0, 50) + '...',
        type: exam.questions[0].type,
        correctAnswer: exam.questions[0].correctAnswer
      });
    }
    console.log("Question IDs:", exam.questions.map(q => q._id || q));
    if (exam.questions.length > 0) {
      console.log("First question sample:", exam.questions[0]);
    }
    console.log("======================");

    // Check if user has access to this exam based on their level
    if (!user.isAdmin) {
      const userLevel = user.level || 'Primary';
      const examLevel = exam.level || 'Primary';

      // Users can only access exams of their own level
      if (userLevel !== examLevel) {
        return res.status(403).send({
          message: `Access denied. You can only access ${userLevel} level exams. This exam is for ${examLevel} level.`,
          success: false,
        });
      }
    }

    res.send({
      message: "Exam fetched successfully",
      data: exam,
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// edit exam by id
router.post("/edit-exam-by-id", authMiddleware, async (req, res) => {
  try {
    await Exam.findByIdAndUpdate(req.body.examId, req.body);
    res.send({
      message: "Exam edited successfully",
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// delete exam by id

router.post("/delete-exam-by-id", authMiddleware, async (req, res) => {
  const session = await mongoose.startSession(); 
  session.startTransaction();

  try {
    const deletedExam = await Exam.findByIdAndDelete(req.body.examId).session(session);

    if (!deletedExam) {
      throw new Error("Exam not found");
    }

    const deleteReportsResult = await Report.deleteMany({ exam: req.body.examId }).session(session);

    await session.commitTransaction();
    session.endSession();

    res.send({
      message: "Exam and related reports deleted successfully",
      success: true,
      deletedReportsCount: deleteReportsResult.deletedCount, 
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    res.status(500).send({
      message: error.message || "Failed to delete exam and reports",
      data: error,
      success: false,
    });
  }
});


// add question to exam

router.post(
  "/add-question-to-exam",
  authMiddleware,
  upload.single("image"),
  async (req, res) => {
    try {
      // Get the uploaded image file
      const questionImage = req.file;

      let imageUrl = null;

      // Define the folder name in S3
      if (questionImage) {
        const folderName = "Questions";

        // Generate a unique filename with the original extension
        const filename = `${folderName}/${uuidv4()}-${
          questionImage.originalname
        }`;

        // S3 upload parameters
        const params = {
          Bucket: process.env.AWS_S3_BUCKET_NAME,
          Key: filename,
          Body: questionImage.buffer,
          ContentType: questionImage.mimetype || "application/octet-stream",
        };

        // Upload image to S3
        const s3Response = await s3.upload(params).promise();

        // Get the S3 image URL
        imageUrl = s3Response.Location;
      }

      // Add question to the Questions collection
      const newQuestion = new Question({
        ...req.body,
        image: imageUrl, // Include the image URL
      });

      const question = await newQuestion.save();

      // Add question to the exam
      const exam = await Exam.findById(req.body.exam);
      if (!exam) {
        return res.status(404).send({
          message: "Exam not found.",
          success: false,
        });
      }
      exam.questions.push(question._id);
      await exam.save();

      // Success response
      res.send({
        message: "Question added successfully",
        success: true,
        data: question,
      });
    } catch (error) {
      // Error response
      res.status(500).send({
        message: error.message,
        data: error,
        success: false,
      });
    }
  }
);

// edit question in exam
router.post(
  "/edit-question-in-exam",
  authMiddleware,
  upload.single("image"),
  async (req, res) => {
    try {
      const { questionId } = req.body;
      const updateData = { ...req.body };

      // Handle image upload if present
      if (req.file) {
        // Define the folder name in S3
        const folderName = "Questions";

        // Generate a unique filename with the original extension
        const filename = `${folderName}/${uuidv4()}-${req.file.originalname}`;

        // S3 upload parameters
        const params = {
          Bucket: process.env.AWS_S3_BUCKET_NAME,
          Key: filename,
          Body: req.file.buffer,
          ContentType: req.file.mimetype || "application/octet-stream",
        };

        // Upload image to S3
        const s3Response = await s3.upload(params).promise();

        // Add the image URL to update data
        updateData.image = s3Response.Location;
      } else {
        // Remove image field if no new image is uploaded
        delete updateData.image;
      }

      // Parse options if it's a string (from FormData)
      if (typeof updateData.options === "string") {
        try {
          updateData.options = JSON.parse(updateData.options);
        } catch (parseError) {
          // If parsing fails, set to null or handle as needed
          updateData.options = null;
        }
      }

      // Remove questionId from update data to prevent mongoose error
      delete updateData.questionId;

      // Update question in Questions collection
      const updatedQuestion = await Question.findByIdAndUpdate(
        questionId,
        updateData,
        { new: true } // Return the updated document
      );

      if (!updatedQuestion) {
        return res.status(404).send({
          message: "Question not found",
          success: false,
        });
      }

      res.send({
        message: "Question edited successfully",
        success: true,
        data: updatedQuestion,
      });
    } catch (error) {
      res.status(500).send({
        message: error.message,
        data: error,
        success: false,
      });
    }
  }
);

// delete question in exam
router.post("/delete-question-in-exam", authMiddleware, async (req, res) => {
  try {
    // delete question in Questions collection
    await Question.findByIdAndDelete(req.body.questionId);

    // delete question in exam
    const exam = await Exam.findById(req.body.examId);
    exam.questions = exam.questions.filter(
      (question) => question._id != req.body.questionId
    );
    await exam.save();
    res.send({
      message: "Question deleted successfully",
      success: true,
    });
  } catch (error) {}
});

// Repair endpoint to fix orphaned questions
router.post("/repair-exam-questions", authMiddleware, async (req, res) => {
  try {
    const { examId } = req.body;

    console.log("🔧 Repairing Exam Questions for ID:", examId);

    // Find the exam
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).send({
        success: false,
        message: "Exam not found"
      });
    }

    // Find all questions that belong to this exam but aren't in the exam.questions array
    const orphanedQuestions = await Question.find({
      exam: examId,
      _id: { $nin: exam.questions || [] }
    });

    console.log("Found orphaned questions:", orphanedQuestions.length);

    if (orphanedQuestions.length > 0) {
      // Add orphaned questions to the exam
      const orphanedIds = orphanedQuestions.map(q => q._id);
      exam.questions.push(...orphanedIds);
      await exam.save();

      console.log("✅ Repaired exam - added", orphanedQuestions.length, "questions");

      res.send({
        success: true,
        message: `Repaired exam: added ${orphanedQuestions.length} orphaned questions`,
        data: {
          addedQuestions: orphanedQuestions.length,
          totalQuestions: exam.questions.length
        }
      });
    } else {
      res.send({
        success: true,
        message: "No orphaned questions found - exam is already correct",
        data: {
          totalQuestions: exam.questions.length
        }
      });
    }

  } catch (error) {
    console.error("Repair error:", error);
    res.status(500).send({
      success: false,
      message: error.message
    });
  }
});

// Debug endpoint to check exam questions
router.post("/debug-exam-questions", authMiddleware, async (req, res) => {
  try {
    const { examId } = req.body;

    console.log("🔍 Debugging Exam Questions for ID:", examId);

    // 1. Check the exam
    const exam = await Exam.findById(examId);
    console.log("Exam found:", !!exam);
    console.log("Questions in exam:", exam?.questions?.length || 0);

    // 2. Check all questions for this exam
    const questionsForExam = await Question.find({ exam: examId });
    console.log("Questions in database for this exam:", questionsForExam.length);

    // 3. Check AI generations for this exam
    const { AIQuestionGeneration } = require("../models/aiQuestionGenerationModel");
    const aiGenerations = await AIQuestionGeneration.find({ examId: examId });
    console.log("AI generations found:", aiGenerations.length);

    // 4. Check for orphaned questions
    const orphanedQuestions = await Question.find({
      exam: examId,
      _id: { $nin: exam?.questions || [] }
    });
    console.log("Orphaned questions found:", orphanedQuestions.length);

    res.send({
      success: true,
      data: {
        exam: {
          found: !!exam,
          name: exam?.name,
          questionsCount: exam?.questions?.length || 0,
          questionIds: exam?.questions || []
        },
        questionsInDatabase: {
          count: questionsForExam.length,
          questions: questionsForExam.map(q => ({
            id: q._id,
            name: q.name,
            answerType: q.answerType,
            isAIGenerated: q.isAIGenerated,
            hasOptions: !!q.options,
            correctOption: q.correctOption,
            correctAnswer: q.correctAnswer
          }))
        },
        aiGenerations: {
          count: aiGenerations.length,
          generations: aiGenerations.map(gen => ({
            id: gen._id,
            status: gen.generationStatus,
            totalQuestions: gen.generatedQuestions.length,
            approvedQuestions: gen.generatedQuestions.filter(q => q.approved).length
          }))
        },
        orphanedQuestions: {
          count: orphanedQuestions.length,
          questions: orphanedQuestions.map(q => ({
            id: q._id,
            name: q.name,
            answerType: q.answerType
          }))
        }
      }
    });

  } catch (error) {
    console.error("Debug error:", error);
    res.status(500).send({
      success: false,
      message: error.message
    });
  }
});

// Get exam statistics (pass count, total attempts, etc.)
router.get("/stats/:id", authMiddleware, async (req, res) => {
  try {
    const examId = req.params.id;
    const Report = require("../models/reportModel");

    // Get all reports for this exam
    const reports = await Report.find({ exam: examId });

    // Calculate statistics
    const totalAttempts = reports.length;
    const passedCount = reports.filter(report => report.result?.verdict === 'Pass').length;
    const failedCount = totalAttempts - passedCount;
    const passRate = totalAttempts > 0 ? Math.round((passedCount / totalAttempts) * 100) : 0;

    // Get unique users who passed
    const uniquePassedUsers = new Set();
    reports.forEach(report => {
      if (report.result?.verdict === 'Pass') {
        uniquePassedUsers.add(report.user.toString());
      }
    });

    const stats = {
      totalAttempts,
      passedCount,
      failedCount,
      passRate,
      uniquePassedUsers: uniquePassedUsers.size
    };

    res.send({
      message: "Exam statistics retrieved successfully",
      success: true,
      data: stats
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Search all exams endpoint
router.post("/search-exams", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    if (!user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const { searchTerm } = req.body;

    console.log(`🔍 Searching for exams with term: ${searchTerm}`);

    // Find all exams that match the search term
    const allExams = await Exam.find({
      name: { $regex: searchTerm, $options: 'i' }
    }).populate('questions');

    const examDetails = allExams.map(exam => ({
      id: exam._id,
      name: exam.name,
      level: exam.level,
      class: exam.class,
      subject: exam.subject,
      questionsCount: exam.questions.length,
      questionIds: exam.questions.map(q => q._id),
      createdAt: exam.createdAt,
      updatedAt: exam.updatedAt,
      hasQuestions: exam.questions.length > 0
    }));

    console.log(`Found ${allExams.length} exams matching "${searchTerm}"`);
    examDetails.forEach(exam => {
      console.log(`- ${exam.name}: Level=${exam.level}, Class=${exam.class}, Questions=${exam.questionsCount}`);
    });

    res.send({
      message: `Found ${allExams.length} exams matching "${searchTerm}"`,
      success: true,
      data: {
        searchTerm,
        totalFound: allExams.length,
        exams: examDetails
      }
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Debug endpoint to check specific exam
router.post("/debug-specific-exam", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    if (!user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const { examName } = req.body;

    console.log(`🔍 Debugging exam: ${examName}`);

    // Find exam by name
    const exam = await Exam.findOne({ name: examName }).populate('questions');

    if (!exam) {
      return res.send({
        message: "Exam not found",
        success: false,
        debug: {
          searchedName: examName,
          found: false
        }
      });
    }

    // Get all exams with similar names
    const similarExams = await Exam.find({
      name: { $regex: examName.substring(0, 4), $options: 'i' }
    }).populate('questions');

    const debugInfo = {
      exam: {
        id: exam._id,
        name: exam.name,
        level: exam.level,
        class: exam.class,
        subject: exam.subject,
        questionsCount: exam.questions.length,
        questionIds: exam.questions.map(q => q._id),
        createdAt: exam.createdAt,
        updatedAt: exam.updatedAt
      },
      similarExams: similarExams.map(e => ({
        id: e._id,
        name: e.name,
        level: e.level,
        class: e.class,
        questionsCount: e.questions.length
      })),
      totalSimilarExams: similarExams.length
    };

    console.log("Debug info:", JSON.stringify(debugInfo, null, 2));

    res.send({
      message: "Debug info retrieved",
      success: true,
      data: debugInfo
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Test endpoint to manually trigger exam notification
router.post("/test-notification", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    if (!user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const { examId } = req.body;

    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    // Send notification
    try {
      await NotificationService.notifyNewExam(exam);
      res.send({
        message: "Test notification sent successfully",
        success: true,
      });
    } catch (notifError) {
      res.status(500).send({
        message: "Failed to send notification: " + notifError.message,
        success: false,
      });
    }
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Check why specific exam is not visible to user
router.post("/check-exam-visibility", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { examName } = req.body;

    const User = require("../models/userModel");
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Find the specific exam
    const exam = await Exam.findOne({ name: examName }).populate('questions');

    if (!exam) {
      return res.send({
        message: `Exam "${examName}" not found`,
        success: false,
        data: {
          examExists: false,
          examName
        }
      });
    }

    // Check all visibility conditions
    const userLevel = user.level || 'Primary';
    const levelMatch = exam.level === userLevel;
    const hasQuestions = exam.questions && exam.questions.length > 0;
    const isAdmin = user.isAdmin;
    const isBlocked = user.isBlocked;

    // Determine if exam would be visible
    let wouldBeVisible = true;
    let reasons = [];

    // Level check
    if (!levelMatch && !isAdmin) {
      wouldBeVisible = false;
      reasons.push(`Level mismatch: Exam level "${exam.level}" != User level "${userLevel}"`);
    }

    // Questions check
    if (!hasQuestions && !isAdmin) {
      wouldBeVisible = false;
      reasons.push(`No questions: Exam has ${exam.questions.length} questions`);
    }

    // User blocked check
    if (isBlocked) {
      wouldBeVisible = false;
      reasons.push(`User is blocked`);
    }

    const result = {
      examExists: true,
      examName: exam.name,
      examLevel: exam.level,
      examClass: exam.class,
      examSubject: exam.subject,
      questionsCount: exam.questions.length,
      userLevel: userLevel,
      userClass: user.class,
      userIsAdmin: isAdmin,
      userIsBlocked: isBlocked,
      checks: {
        levelMatch,
        hasQuestions,
        userNotBlocked: !isBlocked
      },
      wouldBeVisible,
      reasons: reasons.length > 0 ? reasons : ['Exam should be visible'],
      solution: wouldBeVisible ? 'Exam should be visible - check client-side filtering' : 'Fix the issues listed in reasons'
    };

    console.log("=== EXAM VISIBILITY CHECK ===");
    console.log(`Exam: ${examName}`);
    console.log(`Exam Level: ${exam.level}, User Level: ${userLevel}`);
    console.log(`Questions: ${exam.questions.length}`);
    console.log(`Level Match: ${levelMatch}`);
    console.log(`Has Questions: ${hasQuestions}`);
    console.log(`Would Be Visible: ${wouldBeVisible}`);
    console.log(`Reasons: ${reasons.join(', ')}`);
    console.log("=============================");

    res.send({
      message: "Exam visibility checked",
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Check current user admin status
router.post("/check-admin-status", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    const User = require("../models/userModel");
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    console.log("=== ADMIN STATUS CHECK ===");
    console.log(`User ID: ${userId}`);
    console.log(`User Name: ${user.name}`);
    console.log(`User Email: ${user.email}`);
    console.log(`Is Admin: ${user.isAdmin}`);
    console.log(`Is Blocked: ${user.isBlocked}`);
    console.log(`User Level: ${user.level}`);
    console.log("==========================");

    res.send({
      message: "Admin status checked",
      success: true,
      data: {
        userId: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin,
        isBlocked: user.isBlocked,
        level: user.level,
        class: user.class,
        hasAdminAccess: user.isAdmin && !user.isBlocked
      }
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Fix exam level to match user level
router.post("/fix-exam-level", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { examName, newLevel } = req.body;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    // Find and update the exam
    const exam = await Exam.findOne({ name: examName });

    if (!exam) {
      return res.status(404).send({
        message: `Exam "${examName}" not found`,
        success: false,
      });
    }

    const oldLevel = exam.level;
    exam.level = newLevel;
    await exam.save();

    console.log(`✅ Updated exam "${examName}" level: ${oldLevel} → ${newLevel}`);

    res.send({
      message: `Exam level updated successfully`,
      success: true,
      data: {
        examName: exam.name,
        oldLevel,
        newLevel,
        examId: exam._id
      }
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
