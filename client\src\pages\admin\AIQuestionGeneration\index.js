import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { Card, Button, Row, Col, Statistic, Table, Tag, Space, message } from "antd";
import { 
  FaRobot, 
  FaQuestionCircle, 
  FaHistory, 
  FaCog, 
  FaPlus,
  FaEye,
  FaCheck,
  FaTimes,
  FaTrash
} from "react-icons/fa";
import PageTitle from "../../../components/PageTitle";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getGenerationHistory, deleteGeneration } from "../../../apicalls/aiQuestions";
import QuestionGenerationForm from "./QuestionGenerationForm";
import QuestionPreview from "./QuestionPreview";
import "./AIQuestionGeneration.css";

function AIQuestionGeneration() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeView, setActiveView] = useState("dashboard");
  const [generationHistory, setGenerationHistory] = useState([]);
  const [selectedGeneration, setSelectedGeneration] = useState(null);
  const [stats, setStats] = useState({
    totalGenerations: 0,
    totalQuestions: 0,
    approvedQuestions: 0,
    pendingReview: 0,
  });

  useEffect(() => {
    fetchGenerationHistory();
  }, []);

  const fetchGenerationHistory = async () => {
    try {
      dispatch(ShowLoading());
      const response = await getGenerationHistory({ limit: 20 });
      if (response.success) {
        setGenerationHistory(response.data.generations);
        calculateStats(response.data.generations);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error("Failed to fetch generation history");
    } finally {
      dispatch(HideLoading());
    }
  };

  const handleDeleteGeneration = async (generationId) => {
    try {
      dispatch(ShowLoading());
      const response = await deleteGeneration(generationId);
      if (response.success) {
        message.success("Generation deleted successfully");
        await fetchGenerationHistory(); // Refresh the list
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error("Failed to delete generation");
    } finally {
      dispatch(HideLoading());
    }
  };

  const calculateStats = (generations) => {
    const stats = generations.reduce((acc, gen) => {
      acc.totalGenerations += 1;
      acc.totalQuestions += gen.generatedQuestions.length;
      acc.approvedQuestions += gen.generatedQuestions.filter(q => q.approved).length;
      acc.pendingReview += gen.generationStatus === "completed" ? 1 : 0;
      return acc;
    }, {
      totalGenerations: 0,
      totalQuestions: 0,
      approvedQuestions: 0,
      pendingReview: 0,
    });
    setStats(stats);
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: "orange",
      in_progress: "blue",
      completed: "green",
      failed: "red",
      cancelled: "gray",
    };
    return colors[status] || "default";
  };

  const historyColumns = [
    {
      title: "Generation ID",
      dataIndex: "_id",
      key: "_id",
      render: (id) => id.slice(-8),
    },
    {
      title: "Exam",
      dataIndex: ["examId", "name"],
      key: "examName",
    },
    {
      title: "Questions",
      dataIndex: "generatedQuestions",
      key: "questionCount",
      render: (questions) => questions.length,
    },
    {
      title: "Status",
      dataIndex: "generationStatus",
      key: "status",
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: "Created",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => {
        const hasApprovedQuestions = record.generatedQuestions.some(q => q.approved);
        return (
          <Space>
            <Button
              type="link"
              icon={<FaEye />}
              onClick={() => {
                setSelectedGeneration(record);
                setActiveView("preview");
              }}
            >
              Preview
            </Button>
            {!hasApprovedQuestions && (
              <Button
                type="link"
                danger
                icon={<FaTrash />}
                onClick={() => {
                  if (window.confirm("Are you sure you want to delete this generation? This action cannot be undone.")) {
                    handleDeleteGeneration(record._id);
                  }
                }}
                title="Delete generation (only available for unapproved questions)"
              >
                Delete
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  const renderDashboard = () => (
    <div className="ai-question-dashboard">
      <Row gutter={[16, 16]} className="mb-4">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Generations"
              value={stats.totalGenerations}
              prefix={<FaRobot />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Questions Generated"
              value={stats.totalQuestions}
              prefix={<FaQuestionCircle />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Approved Questions"
              value={stats.approvedQuestions}
              prefix={<FaCheck />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending Review"
              value={stats.pendingReview}
              prefix={<FaTimes />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card
            title="Generate New Questions"
            className="action-card"
            actions={[
              <Button
                type="primary"
                icon={<FaPlus />}
                onClick={() => setActiveView("generate")}
              >
                Start Generation
              </Button>
            ]}
          >
            <p>Create AI-generated questions for your exams using advanced language models.</p>
            <ul>
              <li>Multiple choice questions</li>
              <li>Fill in the blank questions</li>
              <li>Picture-based questions</li>
              <li>Tanzania syllabus compliant</li>
            </ul>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card
            title="Generation History"
            className="action-card"
            actions={[
              <Button
                icon={<FaHistory />}
                onClick={() => setActiveView("history")}
              >
                View History
              </Button>
            ]}
          >
            <p>Review and manage your previous question generations.</p>
            <ul>
              <li>Track generation status</li>
              <li>Preview generated questions</li>
              <li>Approve or reject questions</li>
              <li>Add approved questions to exams</li>
            </ul>
          </Card>
        </Col>
      </Row>

      <Card title="Recent Generations" className="mt-4">
        <Table
          dataSource={generationHistory.slice(0, 5)}
          columns={historyColumns}
          pagination={false}
          rowKey="_id"
        />
        {generationHistory.length > 5 && (
          <div className="text-center mt-3">
            <Button onClick={() => setActiveView("history")}>
              View All Generations
            </Button>
          </div>
        )}
      </Card>
    </div>
  );

  const renderHistory = () => (
    <Card title="Generation History">
      <Table
        dataSource={generationHistory}
        columns={historyColumns}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        rowKey="_id"
      />
    </Card>
  );

  const renderContent = () => {
    switch (activeView) {
      case "generate":
        return (
          <QuestionGenerationForm
            onBack={() => setActiveView("dashboard")}
            onSuccess={() => {
              setActiveView("dashboard");
              fetchGenerationHistory();
            }}
          />
        );
      case "preview":
        return (
          <QuestionPreview
            generation={selectedGeneration}
            onBack={() => setActiveView("dashboard")}
            onSuccess={() => {
              setActiveView("dashboard");
              fetchGenerationHistory();
            }}
          />
        );
      case "history":
        return renderHistory();
      default:
        return renderDashboard();
    }
  };

  return (
    <div className="ai-question-generation">
      <PageTitle title="AI Question Generation" />
      
      {activeView === "dashboard" && (
        <div className="page-header">
          <h2>AI Question Generation Dashboard</h2>
          <p>Generate high-quality questions using artificial intelligence</p>
        </div>
      )}

      {renderContent()}
    </div>
  );
}

export default AIQuestionGeneration;
