require('dotenv').config();
const mongoose = require('mongoose');
const Question = require('./models/questionModel');
const Exam = require('./models/examModel');

async function testQuestionCreation() {
  try {
    console.log('🧪 Testing question creation process...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find or create a test exam
    let testExam = await Exam.findOne({ name: 'PDF Test Exam' });
    if (!testExam) {
      testExam = new Exam({
        name: 'PDF Test Exam',
        duration: 60,
        category: 'Mathematics',
        subject: 'Mathematics',
        level: 'Primary',
        class: '5',
        totalMarks: 100,
        passingMarks: 60,
        questions: []
      });
      await testExam.save();
      console.log('✅ Created test exam:', testExam._id);
    } else {
      console.log('✅ Using existing test exam:', testExam._id);
    }
    
    // Test creating a question like PDF extraction would
    console.log('\n📝 Testing question creation...');
    
    const questionData = {
      name: "What is the capital of Tanzania?",
      type: "mcq",
      options: {
        A: "Dar es Salaam",
        B: "Dodoma", 
        C: "Arusha",
        D: "Mwanza"
      },
      correctAnswer: "B",
      topic: "Geography",
      classLevel: "5",
      exam: testExam._id,
      isAIGenerated: true,
      generationSource: "pdf_extraction",
      extractionConfidence: 0.95,
      duration: 90,
      createdBy: "ai"
    };
    
    console.log('📋 Question data to save:', JSON.stringify(questionData, null, 2));
    
    // Create and save question
    const newQuestion = new Question(questionData);
    console.log('💾 Saving question...');
    const savedQuestion = await newQuestion.save();
    console.log('✅ Question saved with ID:', savedQuestion._id);
    
    // Add to exam
    console.log('🔗 Adding question to exam...');
    testExam.questions.push(savedQuestion._id);
    await testExam.save();
    console.log('✅ Question added to exam');
    
    // Verify by fetching exam with populated questions
    console.log('🔍 Verifying by re-fetching exam...');
    const verifyExam = await Exam.findById(testExam._id).populate('questions');
    console.log(`✅ Exam now has ${verifyExam.questions.length} questions`);
    
    if (verifyExam.questions.length > 0) {
      const lastQuestion = verifyExam.questions[verifyExam.questions.length - 1];
      console.log('📝 Last question details:');
      console.log('- Name:', lastQuestion.name);
      console.log('- Type:', lastQuestion.type);
      console.log('- Correct Answer:', lastQuestion.correctAnswer);
      console.log('- Generation Source:', lastQuestion.generationSource);
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up test data...');
    await Question.findByIdAndDelete(savedQuestion._id);
    testExam.questions.pull(savedQuestion._id);
    await testExam.save();
    console.log('✅ Test question removed');
    
    console.log('\n🎉 Question creation test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

testQuestionCreation();
