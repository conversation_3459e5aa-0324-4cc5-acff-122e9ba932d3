{"ast": null, "code": "import { axiosInstance } from \"./index\";\n\n// Extract questions from PDF\nexport const extractQuestionsFromPDF = async formData => {\n  try {\n    const response = await axiosInstance.post('/api/pdf-extraction/extract-questions', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    var _error$response;\n    return ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || {\n      success: false,\n      message: 'Network error occurred'\n    };\n  }\n};\n\n// Approve and save extracted questions\nexport const approveExtractedQuestions = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/pdf-extraction/approve-questions', payload);\n    return response.data;\n  } catch (error) {\n    var _error$response2;\n    return ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {\n      success: false,\n      message: 'Network error occurred'\n    };\n  }\n};\n\n// Get extraction history for an exam\nexport const getExtractionHistory = async examId => {\n  try {\n    const response = await axiosInstance.get(`/api/pdf-extraction/extraction-history/${examId}`);\n    return response.data;\n  } catch (error) {\n    var _error$response3;\n    return ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || {\n      success: false,\n      message: 'Network error occurred'\n    };\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "extractQuestionsFromPDF", "formData", "response", "post", "headers", "data", "error", "_error$response", "success", "message", "approveExtractedQuestions", "payload", "_error$response2", "getExtractionHistory", "examId", "get", "_error$response3"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/pdfExtraction.js"], "sourcesContent": ["import { axiosInstance } from \"./index\";\n\n// Extract questions from PDF\nexport const extractQuestionsFromPDF = async (formData) => {\n  try {\n    const response = await axiosInstance.post('/api/pdf-extraction/extract-questions', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { \n      success: false, \n      message: 'Network error occurred' \n    };\n  }\n};\n\n// Approve and save extracted questions\nexport const approveExtractedQuestions = async (payload) => {\n  try {\n    const response = await axiosInstance.post('/api/pdf-extraction/approve-questions', payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { \n      success: false, \n      message: 'Network error occurred' \n    };\n  }\n};\n\n// Get extraction history for an exam\nexport const getExtractionHistory = async (examId) => {\n  try {\n    const response = await axiosInstance.get(`/api/pdf-extraction/extraction-history/${examId}`);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { \n      success: false, \n      message: 'Network error occurred' \n    };\n  }\n};\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,SAAS;;AAEvC;AACA,OAAO,MAAMC,uBAAuB,GAAG,MAAOC,QAAQ,IAAK;EACzD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,uCAAuC,EAAEF,QAAQ,EAAE;MAC3FG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAC,eAAA;IACd,OAAO,EAAAA,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,KAAI;MAC7BG,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,yBAAyB,GAAG,MAAOC,OAAO,IAAK;EAC1D,IAAI;IACF,MAAMT,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,uCAAuC,EAAEQ,OAAO,CAAC;IAC3F,OAAOT,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAM,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAN,KAAK,CAACJ,QAAQ,cAAAU,gBAAA,uBAAdA,gBAAA,CAAgBP,IAAI,KAAI;MAC7BG,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,oBAAoB,GAAG,MAAOC,MAAM,IAAK;EACpD,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAMH,aAAa,CAACgB,GAAG,CAAE,0CAAyCD,MAAO,EAAC,CAAC;IAC5F,OAAOZ,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAU,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAV,KAAK,CAACJ,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBX,IAAI,KAAI;MAC7BG,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}